#!/usr/bin/env bash
set -e

echo "🔄 Waiting for PostgreSQL at ${POSTGRES_HOST}:${POSTGRES_PORT} ..."
until pg_isready -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" >/dev/null 2>&1; do
  sleep 1
done
echo "✅ PostgreSQL is ready"

echo "🔑 Setting up Google Cloud credentials"
if [ -n "$GOOGLE_CREDENTIALS_BASE64" ]; then
    # base64デコード（Linux環境用）
    echo "$GOOGLE_CREDENTIALS_BASE64" | base64 -d > /tmp/google-credentials.json 2>/dev/null || \
    # macOS環境用のフォールバック
    echo "$GOOGLE_CREDENTIALS_BASE64" | python3 -m base64 -d > /tmp/google-credentials.json

    export GOOGLE_APPLICATION_CREDENTIALS=/tmp/google-credentials.json
    chmod 600 /tmp/google-credentials.json
    echo "✅ Google Cloud credentials configured"

    # 認証ファイルの内容確認（デバッグ用）
    if python3 -c "import json; json.load(open('/tmp/google-credentials.json'))" 2>/dev/null; then
        echo "✅ Credentials file is valid JSON"
    else
        echo "❌ Credentials file is invalid JSON"
    fi
else
    echo "⚠️  GOOGLE_CREDENTIALS_BASE64 not set, Google Vision API will not be available"
fi

echo "⚙️  Running Alembic migrations"
alembic upgrade head

echo "🚀 Starting Uvicorn"
exec uvicorn agent.main:app --host 0.0.0.0 --port 8000