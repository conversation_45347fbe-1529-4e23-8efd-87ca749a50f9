<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Privacy Policy | My AI Agents</title>
  <style>
    body { max-width: 800px; margin: 2em auto; font-family: sans-serif; line-height:1.6; }
    h1,h2,h3 { margin-top:1.5em; }
    a { color: #0066cc; text-decoration: none; }
    a:hover { text-decoration: underline; }
  </style>
</head>
<body>

  <h1>Privacy Policy</h1>
  <p>Last updated: June 3, 2025</p>

  <p>My AI Agents (“we”, “our”, “us”) operates <a href="https://myaiagents.duckdns.org">https://myaiagents.duckdns.org</a> (the “Site”) and is committed to protecting your privacy. This Policy explains how we collect, use, disclose, and safeguard your information.</p>

  <h2>1. Information We Collect</h2>
  <ul>
    <li><strong>Account Information:</strong> Your email address, hashed password, and profile data you provide when you register.</li>
    <li><strong>OAuth Credentials:</strong> Google OAuth tokens (Gmail / Calendar) that allow us to access your Gmail messages and Calendar events on your behalf.</li>
    <li><strong>Email &amp; Calendar Data:</strong> Plain-text excerpts (max 5 KB per message) of the last 7 days of emails and upcoming calendar events needed for task extraction and scheduling.</li>
    <li><strong>Usage Data:</strong> IP address, access times, and API usage recorded in server logs for analytics and abuse prevention.</li>
  </ul>

  <h2>2. How We Use Your Information</h2>
  <ul>
    <li>Authenticate and identify you via our JWT-based login system.</li>
    <li>Fetch Gmail messages and extract tasks using large-language-model (LLM) APIs (see Section&nbsp;3).</li>
    <li>Retrieve Google Calendar events and propose conflict-free schedules.</li>
    <li>Maintain and improve our service, including debugging, analytics, and new-feature development.</li>
    <li>Prevent fraud, abuse, and unauthorized access.</li>
  </ul>

  <h2>3. Third-Party Processing <!-- NEW --></h2>
  <p>To extract tasks and generate schedules we send <em>minimal, sanitized text excerpts</em> of your emails and events to the following provider:</p>
  <ul>
    <li><strong>OpenAI LLM API</strong> – operated by OpenAI, L.L.C. (servers located in the United States). OpenAI stores the data only transiently for processing and does <em>not</em> use it to train its models, in accordance with <a href="https://openai.com/policies/api-data-usage-policies" target="_blank">their API data-usage policy</a>.</li>
  </ul>
  <p>By connecting Gmail / Calendar you explicitly consent to this transfer. You may withdraw consent at any time by disconnecting the integration (Settings ▶ Disconnect) or revoking access in your Google Account.</p>

  <h2>4. Cookies &amp; Logs</h2>
  <p>We use cookies to manage user sessions and CSRF protection. Standard server logs (IP, user-agent, timestamps) are stored for up to 30 days and then deleted.</p>

  <h2>5. Security Measures</h2>
  <p>All traffic is encrypted via TLS 1.2+. OAuth tokens are AES-256–encrypted at rest and access-controlled to operational staff only.</p>

  <h2>6. Data Retention</h2>
  <p>
    • OAuth tokens are kept while your account is active.<br>
    • Cached email / calendar excerpts are deleted within 24 hours after processing.<br>
    • You may delete your account at any time; all associated data is purged within 90 days.<br>
  </p>

  <h2>7. Your Rights &amp; Choices</h2>
  <ul>
    <li>Access, update, or delete your account information via the user interface or API.</li>
    <li>Disconnect Gmail / Calendar at any time (Settings ▶ Disconnect).</li>
    <li>Revoke Google OAuth permissions in <a href="https://myaccount.google.com/permissions" target="_blank">Google Account ▶ Security ▶ Third-party access</a>.</li>
    <li>Request export or erasure of your data by emailing us (see below).</li>
  </ul>

  <h2>8. Changes to This Policy</h2>
  <p>We may update this Privacy Policy periodically. If we make material changes we will notify you via the Site or email.</p>

  <h2>9. Contact Us</h2>
  <p>If you have questions or concerns about this Policy, please contact:</p>
  <p><strong>Email:</strong> <EMAIL></p>

</body>
</html>