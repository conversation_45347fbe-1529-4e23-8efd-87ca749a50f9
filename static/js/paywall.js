import { isPaidUser, buy } from "/static/js/auth.js";  // buy() は既存の checkout 関数

(function(){
  const bd = document.getElementById("paywallBackdrop");
  const btnClose = document.getElementById("paywallClose");
  const btnBuy   = document.getElementById("paywallBuy");

  btnClose.onclick = ()=> bd.classList.add("hidden");
  btnBuy  .onclick = ()=> buy();          // Stripe checkout へ

  /* ---- 公開 API ------------------------------------------------ */
  window.openPaywall = function(){
    bd.classList.remove("hidden");
  };
})();