// todos.js

;(async function(){
  // デフォルトカテゴリ
  const DEFAULT_CATS = ["実験","会議","教育","管理","その他"];

  // 1) JWT 取得
  const jwt = localStorage.getItem('jwt');
  if (!jwt) {
    alert('Please log in first');
    return location.href = '/';
  }

  // 2) API ヘルパ
  async function api(path, opts = {}) {
    opts.headers = {
      ...(opts.headers||{}),
      'Authorization': 'Bearer ' + jwt,
      'Content-Type': 'application/json'
    };
    const res = await fetch(path, opts);
    if (res.status === 401) {
      alert('Session expired. Please log in again.');
      localStorage.removeItem('jwt');
      return location.href = '/';
    }
    if (!res.ok) {
      const text = await res.text();
      console.error(`API ${path} error ${res.status}:`, text);
      throw new Error(`API ${path} returned ${res.status}`);
    }
    return res;
  }

  // ── カテゴリ管理 ─────────────────────────────────
  // サーバー登録済みカテゴリリスト（名前だけ）
  let categories = [];
  const addCategoryBtn   = document.getElementById('addCategoryBtn');
  const newCategoryName  = document.getElementById('newCategoryName');
  const defaultCatsDiv   = document.getElementById('defaultCats');
  const defaultCatButtons= document.getElementById('defaultCatButtons');
  const categoryList     = document.getElementById('categoryList');
  const categoryFilterEl   = document.getElementById('categoryFilter');

  // カテゴリ一覧を取得して UI 更新
  async function loadCategories() {
    try {
      const res  = await api('/api/categories');
      const data = await res.json();  // [{category_id,name,order},…] or []

      if (Array.isArray(data) && data.length > 0) {
        categories      = data.map(c => c.name);
        defaultCatsDiv.style.display = 'none';
      } else {
        // DBが空ならデフォルトを使いつつ、ボタンを表示
        categories = [...DEFAULT_CATS];
        defaultCatsDiv.style.display = 'block';
        defaultCatButtons.innerHTML = DEFAULT_CATS
          .map(c => `<button class="def-cat">${c}</button>`)
          .join('');
        // ボタンに新規カテゴリ追加をバインド
        defaultCatButtons.querySelectorAll('.def-cat').forEach(btn => {
          btn.onclick = async () => {
            newCategoryName.value = btn.textContent;
            await addCategoryBtn.onclick();  // 既存ハンドラ呼び出し
          };
        });
      }
      // 管理パネルにカテゴリ一覧を描画
      categoryList.innerHTML = '';
      data.forEach(c => {
        const li = document.createElement('li');
        li.innerHTML = `
          <span>${c.name}</span>
          <button class="del-cat" data-id="${c.category_id}">✕</button>
        `;
        categoryList.append(li);
      });
      // 削除ボタン
      categoryList.querySelectorAll('.del-cat').forEach(btn => {
        btn.onclick = async () => {
          const id = btn.dataset.id;
          if (!confirm('本当に削除しますか？')) return;
          await api(`/api/categories/${id}`, { method:'DELETE' });
          await loadCategories();
        };
      });

      // フィルターの選択肢も更新
      if (categoryFilterEl) {
        categoryFilterEl.innerHTML = '<option value="">All</option>' +
          categories.map(c=>`<option value="${c}">${c}</option>`).join('');
      }

    } catch(e) {
      console.error('Failed to load categories:', e);
    }
  }

  // 管理パネルにカテゴリを描画
  function renderCategories(data) {
    const ul = document.getElementById('categoryList');
    if (!ul) return;
    ul.innerHTML = '';
    data.forEach(c => {
      const li = document.createElement('li');
      li.innerHTML = `
        <span class="cat-name">${c.name}</span>
        <button class="del-cat" data-id="${c.category_id}">✕</button>
      `;
      ul.appendChild(li);
    });
    // 削除ボタンにイベントバインド
    ul.querySelectorAll('.del-cat').forEach(btn => {
      btn.onclick = async () => {
        const id = btn.dataset.id;
        if (!confirm('本当に削除しますか？')) return;
        try {
          await api(`/api/categories/${id}`, { method:'DELETE' });
          await loadCategories();
        } catch(err) {
          console.error('Failed to delete category:', err);
          alert('カテゴリの削除に失敗しました');
        }
      };
    });
  }

  // カテゴリ追加ボタン
  if (addCategoryBtn && newCategoryName) {
    addCategoryBtn.onclick = async () => {
      const name = newCategoryName.value.trim();
      if (!name) { alert('カテゴリ名を入力してください'); return; }
      try {
        await api('/api/categories', {
          method: 'POST',
          body: JSON.stringify({ name, order: null })
        });
        newCategoryName.value = '';
        await loadCategories();
      } catch(err) {
        console.error('Failed to add category:', err);
        alert('カテゴリの追加に失敗しました');
      }
    };
  }

  // ── ボタン＆ステータス要素取得と初期化 ───────────────────────
  const btns = {
    connectGmail:  document.getElementById('connectGmailBtn'),
    connectCal:    document.getElementById('connectCalBtn'),
    disconnectG:   document.getElementById('discGmailBtn'),
    disconnectCal: document.getElementById('discCalBtn'),
    load:          document.getElementById('loadBtn'),
  };
  const gmailStatusEl = document.getElementById('gmailStatus');
  const calStatusEl   = document.getElementById('calStatus');
  Object.values(btns).forEach(b => b && (b.style.display = 'none'));
  
  // 3.1) 非表示タスク表示トグル
  let showHidden = false;
  const showHiddenCheckbox = document.getElementById('showHiddenCheckbox');
  if (showHiddenCheckbox) {
    showHidden = showHiddenCheckbox.checked;
    showHiddenCheckbox.addEventListener('change', () => {
      showHidden = showHiddenCheckbox.checked;
      renderTodos();
    });
  }
  // 3.2) カテゴリフィルタ変更時にも再描画
  if (categoryFilterEl) {
    categoryFilterEl.addEventListener('change', () => {
      renderTodos();
    });
  }

  // 4) 接続状況取得
  let me;
  try {
    const res = await api('/auth/me');
    me = await res.json();
  } catch(e) {
    console.error('Failed to fetch /auth/me:', e);
    alert('Failed to get your account info. Try reloading.');
    return;
  }
  const userRole = me.role;

  // 5) ステータス表示（Gmail / Calendar）
  if (gmailStatusEl) {
    gmailStatusEl.innerHTML = me.gmail_connected
      ? `✅ ${me.gmail_address||me.email} Last Sync: ${me.last_gmail_sync?new Date(me.last_gmail_sync).toLocaleString():''}<br>`
      : '❌ Not connected<br>';
  }
  if (calStatusEl) {
    calStatusEl.innerHTML = me.calendar_connected
      ? `✅ ${me.calendar_address} Last Sync: ${me.last_calendar_sync?new Date(me.last_calendar_sync).toLocaleString():''}<br>`
      : '❌ Not connected<br>';
  }

  // 6) Gmail 接続／切断ボタン
  if (me.gmail_connected) {
    btns.disconnectG.style.display = 'inline-block';
    btns.disconnectG.onclick = async () => {
      await api('/auth/google/disconnect/gmail',{method:'POST'});
      location.reload();
    };
  } else {
    btns.connectGmail.style.display = 'inline-block';
    btns.connectGmail.onclick = () => {
      location.href = `/auth/google/connect/gmail?jwt=${encodeURIComponent(jwt)}`;
    };
  }

  // 7) Calendar 接続／切断ボタン
  if (me.calendar_connected) {
    btns.disconnectCal.style.display = 'inline-block';
    btns.disconnectCal.onclick = async () => {
      await api('/auth/google/disconnect/calendar',{method:'POST'});
      location.reload();
    };
  } else {
    btns.connectCal.style.display = 'inline-block';
    btns.connectCal.onclick = () => {
      location.href = `/auth/google/connect/calendar?jwt=${encodeURIComponent(jwt)}`;
    };
  }

  // 8) Gmail 連携と User role に応じて Load ボタン出す
  if (me.gmail_connected && userRole >= 0) {
    btns.load.style.display = 'inline-block';
    btns.load.onclick = loadAndRender;
  }

  // ページ読み込み時にカテゴリだけ先に取得して管理パネルを表示
  
  await loadCategories();

  // 9) 汎用リスト描画ユーティリティ
  function renderList(elId, items, formatter) {
    const ul = document.getElementById(elId);
    if (!ul) return console.error(`renderList: Element "${elId}" not found`);
    ul.innerHTML = '';
    if (!items || items.length === 0) {
      ul.innerHTML = '<li>(none)</li>';
      return;
    }
    items.forEach(x => {
      const li = document.createElement('li');
      li.innerHTML = formatter(x);
      ul.appendChild(li);
    });
  }

  let allTodos = [];

  // 10) タスク＆スケジュール取得・描画
  async function loadAndRender() {
    const btn     = btns.load;
    let spinner   = document.getElementById('loadSpinner');
    if (!spinner) {
      spinner = document.createElement('span');
      spinner.id = 'loadSpinner';
      spinner.className = 'spinner';
      spinner.textContent = '🔄';
      btn.appendChild(spinner);
    }

    btn.disabled = true;
    spinner.style.display = 'inline-block';

    try {
      // ① 最新のカテゴリを再取得（タスク抽出時の enum 用）
      await loadCategories();

      // ② タスク取得
      const res = await api(`/api/todos?showHidden=true`);
      const data = await res.json();

      // ③ プラン不足時
      if (data.plan_required) {
        if (confirm("Upgrade now?")) {
          api('/billing/checkout',{method:'POST'})
            .then(r=>r.json()).then(x=>window.location.href=x.url)
            .catch(()=>alert("Failed to create checkout session"));
        }
        return;
      }

      // ④ 正常レスポンス
      const { todos, events, schedule } = data;

      // ⑤ 並び替え
      allTodos = todos.slice().sort((a,b)=>{
        if (!a.due) return 1;
        if (!b.due) return -1;
        return new Date(a.due) - new Date(b.due);
      });

      // ⑥ フィルタセレクト更新
      populateCategoryFilter();

      // ⑦ タスク描画
      renderTodos();

      // ⑧ イベント・スケジュール描画
      renderList('eventList', events, e => `${e.summary} (${e.start} – ${e.end})`);
      renderList('schedList', schedule, s => {
        let txt = `${s.task_id}: ${s.start} – ${s.end}`;
        if (s.note) txt += ` (${s.note})`;
        return txt;
      });

    } catch(e) {
      console.error('loadAndRender error:', e);
      alert('Failed to load todos. Check console for details.');
    } finally {
      btn.disabled = false;
      spinner.style.display = 'none';
    }
  }

  // 11) フィルタセレクトにカテゴリを注入
  function populateCategoryFilter() {
    const select = document.getElementById('categoryFilter');
    if (!select) return;
    select.innerHTML = '<option value="">All</option>' +
      categories.map(c => `<option value="${c}">${c}</option>`).join('');
  }

  // 12) タスク一覧描画
  function renderTodos() {
    const sel = categoryFilterEl ? categoryFilterEl.value : "";
    const filtered = sel
      ? allTodos.filter(t => (t.category||"").trim() === sel)
      : allTodos;

    const visibleSet = filtered.filter(t => showHidden || !t.hidden);

    let toShow;
    if (userRole === 0) {
      toShow = [];
    } else if (userRole === 1) {
      toShow = visibleSet.slice(0,5);
    } else {
      toShow = visibleSet;
    }

    const ul = document.getElementById('todoList');
    ul.innerHTML = '';
    if (!toShow.length) {
      ul.innerHTML = '<li>(none)</li>';
      return;
    }

    toShow.forEach(t => {
      const li = document.createElement('li');

      // Hide/Unhide ボタン
      if (t.id) {
        const btn = document.createElement('button');
        btn.textContent = t.hidden ? 'Unhide' : 'Hide';
        btn.className = 'hide-btn';
        btn.onclick = async () => {
          const method = t.hidden ? 'DELETE' : 'PATCH';
          await api(`/api/todos/${encodeURIComponent(t.id)}/hide`,{method});
          t.hidden = !t.hidden;
          renderTodos();
        };
        li.appendChild(btn);
      }

      // カテゴリドロップダウン
      const selCat = document.createElement('select');
      selCat.className = 'cat-select';
      categories.forEach(c => {
        const opt = document.createElement('option');
        opt.value = c;
        opt.textContent = c;
        if (c === t.category) opt.selected = true;
        selCat.appendChild(opt);
      });
      selCat.onchange = async e => {
        const newCat = e.target.value;
        try {
          await api(`/api/todos/${encodeURIComponent(t.id)}`, {
            method: 'PATCH',
            body: JSON.stringify({ category: newCat })
          });
          t.category = newCat;
          renderTodos();
        } catch(err) {
          console.error('Failed to update category:', err);
          alert('カテゴリの更新に失敗しました');
        }
      };
      li.appendChild(selCat);

      // タスク本文等
      const span = document.createElement('span');
      const icon = {1:'',2:'',3:''}[t.priority]||'';
      const due  = t.due ? ` (due: ${t.due})` : '';
      const link = t.link
        ? `<a href="${t.link}" target="_blank">📩</a> `
        : '';
      span.innerHTML = `${link}${icon} ${t.task}${due}`;
      li.appendChild(span);

      // hidden は取り消し線＋薄字
      if (t.hidden) {
        li.classList.add('task--hidden');
      }

      ul.appendChild(li);
    });

    // フッター：課金促進
    const footer = document.getElementById('todoListFooter');
    if ((userRole===0 || userRole===1) && visibleSet.length>5) {
      footer.innerHTML = `
        <div class="subscribe-prompt">
          <em>... subscribe to show more </em>
          <button id="subscribeBtn">Subscribe</button>
        </div>`;
      document.getElementById('subscribeBtn').onclick = async () => {
        try {
          const res = await api('/billing/checkout',{method:'POST'});
          const { url } = await res.json();
          window.location.href = url;
        } catch {
          alert('Subscription failed. Please try again later.');
        }
      };
    } else {
      footer.innerHTML = '';
    }
  }

  // ───────────────────────────────────────────
  // トピック関与者抽出フォームのハンドラ（省略せず維持）
  // ───────────────────────────────────────────
  const topicForm        = document.getElementById('topicForm');
  const topicInput       = document.getElementById('topicInput');
  const topicSpinner     = document.getElementById('topicSpinner');
  const participantsList = document.getElementById('participantsList');
  const noResultEl       = document.getElementById('noResult');

  if (topicForm && topicInput && topicSpinner && participantsList && noResultEl) {
    topicForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      const topic = topicInput.value.trim();
      if (!topic) return;
      participantsList.innerHTML = '';
      noResultEl.style.display = 'none';
      topicSpinner.style.display = 'inline-block';
      try {
        const res  = await api(`/api/participants?topic=${encodeURIComponent(topic)}`);
        const data = await res.json();
        const arr  = Array.isArray(data.participants) ? data.participants : [];
        if (arr.length === 0) {
          noResultEl.style.display = 'block';
        } else {
          arr.forEach(name => {
            const li = document.createElement('li');
            li.textContent = name;
            participantsList.appendChild(li);
          });
        }
      } catch (err) {
        console.error('Failed to extract participants:', err);
        alert('関与者の抽出に失敗しました。');
      } finally {
        topicSpinner.style.display = 'none';
      }
    });
  }

})();