// writer.js
import { isPaidUser, waitForUser, buy } from "/static/js/auth.js";

(() => {
  // ペイウォールを動的に読み込む関数
  const loadPaywall = async function() {
    if (!document.getElementById('paywallBackdrop')) {
      try {
        const response = await fetch('/static/paywall.html');
        const html = await response.text();
        document.body.insertAdjacentHTML('beforeend', html);

        // paywall.jsの機能を手動で初期化
        const bd = document.getElementById("paywallBackdrop");
        const btnClose = document.getElementById("paywallClose");
        const btnBuy = document.getElementById("paywallBuy");

        if (btnClose) {
          btnClose.onclick = () => bd.classList.add("hidden");
          btnClose.disabled = false; // 確実に有効化
        }
        if (btnBuy) {
          btnBuy.onclick = () => buy();
          btnBuy.disabled = false; // 確実に有効化
        }

        // グローバルなopenPaywall関数を定義
        window.openPaywall = function() {
          bd.classList.remove("hidden");
        };
      } catch (error) {
        console.error('Failed to load paywall:', error);
      }
    }
  }

  // ユーザー情報の読み込み完了を待ってからペイウォール判定
  document.addEventListener("DOMContentLoaded", async ()=>{
    try {
      await waitForUser(); // ユーザー情報の読み込み完了を待つ
    } catch (error) {
      console.error('ユーザー情報の読み込みに失敗しました:', error);
    }

    if(!isPaidUser()){
      // ペイウォールを開いて、UI を「閲覧専用」に
      await loadPaywall();
      if (typeof openPaywall === 'function') {
        openPaywall();
      }

      // 1) メインコンテンツのフォームや実行ボタンをすべて disable（ペイウォール関連とヘッダーは除外）
      document.querySelectorAll("main button, section button, main input, section input, main textarea, section textarea, main select, section select")
              .forEach(el => {
                // ペイウォール関連のボタンは除外
                if (el.id !== 'paywallClose' && el.id !== 'paywallBuy') {
                  el.disabled = true;
                }
              });

      // 2) 「クリック → ペイウォール表示」だけ許可したい
      ["btnUpload","toggleEdit","prevPage","nextPage"].forEach(id=>{
        const el = document.getElementById(id);
        if(el){
          el.disabled = false;
          el.onclick  = async e => {
            e.preventDefault();
            await loadPaywall();
            if (typeof openPaywall === 'function') {
              openPaywall();
            }
          };
        }
      });

      // 3) ペイウォールボタンは常に有効にする
      setTimeout(() => {
        const paywallClose = document.getElementById("paywallClose");
        const paywallBuy = document.getElementById("paywallBuy");
        if (paywallClose) paywallClose.disabled = false;
        if (paywallBuy) paywallBuy.disabled = false;
      }, 100);
      // これ以降の本体ロジックを return して読ませない
      return;
    }

    // 有料ユーザーの場合は通常の初期化を実行
    initializeApp();
  });

  // アプリケーションの初期化関数
  function initializeApp() {



  // ===== DOM 取得 =====
  const imgInput      = document.getElementById("imgFiles");
  const btnUpload     = document.getElementById("btnUpload");
  const noteStatus    = document.getElementById("noteStatus");

  const noteSec       = document.getElementById("noteSec");
  const transBox      = document.getElementById("transBox");
  const toggleEdit    = document.getElementById("toggleEdit");
  const qArea         = document.getElementById("qArea");
  const questionList  = document.getElementById("questionList");

  const pageNav       = document.getElementById("pageNav");
  const prevBtn       = document.getElementById("prevPage");
  const nextBtn       = document.getElementById("nextPage");
  const pageIndicator = document.getElementById("pageIndicator");

  const btnMakePaper  = document.getElementById("btnMakePaper");
  const paperSec      = document.getElementById("paperSec");
  const paperViewer   = document.getElementById("paperViewer");
  const feedbackBox   = document.getElementById("feedbackBox");
  const btnRewrite    = document.getElementById("btnRewrite");
  const revisedSec    = document.getElementById("revisedSec");
  const revisedViewer = document.getElementById("revisedViewer");
  const rewriteCount  = document.getElementById("rewriteCount");
  const nextFeedbackBox = document.getElementById("nextFeedbackBox");
  const btnNextRewrite = document.getElementById("btnNextRewrite");
  const btnManualEdit = document.getElementById("btnManualEdit");
  const manualEditSec = document.getElementById("manualEditSec");
  const manualEditBox = document.getElementById("manualEditBox");
  const btnAddCitations = document.getElementById("btnAddCitations");
  const citedPaperSec = document.getElementById("citedPaperSec");
  const citedPaperViewer = document.getElementById("citedPaperViewer");
  const citationFormat = document.getElementById("citationFormat");
  const btnDownloadRIS = document.getElementById("btnDownloadRIS");
  const btnShowReport = document.getElementById("btnShowReport");
  const reportSection = document.getElementById("reportSection");
  const reportViewer = document.getElementById("reportViewer");


  /**
   * API 用 fetch ラッパー
   * 自動で Authorization: Bearer <jwt> を付与する
    */
  async function apiFetch(url, opt = {}) {
    const token = localStorage.getItem("jwt");

    // デバッグ情報を追加
    console.log(`API Request to: ${url}`);
    console.log(`Token available: ${!!token}`);
    if (token) {
      console.log(`Token preview: ${token.substring(0, 20)}...`);
    }

    const headers = Object.assign(
        {},                       // 空 object
        opt.headers || {},        // 呼び出し元が指定したヘッダー
        token ? { Authorization: `Bearer ${token}` } : {}
    );

    const response = await fetch(url, { ...opt, headers });

    // レスポンスのデバッグ情報
    console.log(`Response status: ${response.status}`);
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error: ${errorText}`);
      throw new Error(errorText);
    }

    return response;
  }

  // --- PDF → File[](.png) 変換 --------------------------
  async function pdfToImages(pdfFile, scale = 2) {
    const arrayBuffer = await pdfFile.arrayBuffer();
    const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise;

    const imgFiles = [];
    for (let p = 1; p <= pdf.numPages; p++) {
      const page = await pdf.getPage(p);
      const viewport = page.getViewport({ scale });
      const canvas = document.createElement("canvas");
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      const ctx = canvas.getContext("2d");
      await page.render({ canvasContext: ctx, viewport }).promise;

      // canvas → Blob → File
      const blob = await new Promise(res => canvas.toBlob(res, "image/png"));
      const imgName = `${pdfFile.name.replace(/\.pdf$/i, "")}_${String(p).padStart(3,"0")}.png`;
      imgFiles.push(new File([blob], imgName, { type: "image/png" }));
    }
    return imgFiles;
  }

  // ===== State =====
  let noteId      = null;
  let paperId     = null;
  let notePages   = [];   // 複数ページ分の OCR 結果オブジェクト
  let currentPage = 0;    // 0-based
  let editMode    = false;
  let rewriteCounter = 1; // リライト回数カウンター
  let currentPaper = ""; // 現在の論文テキスト
  let citationData = null; // 引用データ
  let risUrl = null; // RISファイルのURL
  let reportUrl = null; // 詳細レポートのURL

  // ===== 質問リスト描画 =====
  function showQuestions(qText) {
    if (!qText) {
      qArea.classList.add("hidden");
      return;
    }
    
    // 質問エリアを表示
    qArea.classList.remove("hidden");
    questionList.innerHTML = "";
    
    let items = [];
    if (typeof qText === "string") {
      items = qText.split(/\r?\n/).map(s => s.trim()).filter(Boolean);
    } else if (Array.isArray(qText)) {
      items = qText.map(s => String(s).trim()).filter(Boolean);
    } else {
      items = [JSON.stringify(qText)];
    }
    
    items.forEach(line => {
      const li = document.createElement("li");
      li.textContent = line;
      questionList.appendChild(li);
    });
  }

  // ===== ページ描画 =====
  function renderPage(idx) {
    const note = notePages[idx];

    // 転写内容を表示
    transBox.value = note["転写内容"] || "";

    // 復元された表構造があれば表示
    displayReconstructedTables(note);

    // ページインジケーターを更新
    pageIndicator.textContent = `${idx+1} / ${notePages.length}`;
    prevBtn.disabled = idx === 0;
    nextBtn.disabled = idx === notePages.length - 1;

    // OCR情報を表示
    showOCRInfo(note);

    // 復元された表構造があれば表示
    displayReconstructedTables(note);

    // 質問を転写内容の後に表示
    showQuestions(note["ユーザへの質問"]);
  }

  // ===== 復元された表構造の表示 =====
  function displayReconstructedTables(note) {
    // 既存の表示をクリア
    const existingTableDisplay = document.querySelector('.table-display-container');
    if (existingTableDisplay) {
      existingTableDisplay.remove();
    }

    // 復元された表があるかチェック
    if (note["復元された表"] || note["表構造データ"]) {
      const tableContainer = document.createElement('div');
      tableContainer.className = 'table-display-container';

      // トグルボタン
      const toggleBtn = document.createElement('button');
      toggleBtn.className = 'table-toggle-btn';
      toggleBtn.textContent = '📊 復元された表構造を表示';

      // 表構造表示エリア
      const tableDisplay = document.createElement('div');
      tableDisplay.className = 'reconstructed-table';
      tableDisplay.style.display = 'none';

      const tableTitle = document.createElement('h4');
      tableTitle.textContent = '復元された表構造';

      const tableContent = document.createElement('pre');
      tableContent.textContent = note["復元された表"] || "表構造データが利用できません";

      tableDisplay.appendChild(tableTitle);
      tableDisplay.appendChild(tableContent);

      // トグル機能
      let isVisible = false;
      toggleBtn.addEventListener('click', () => {
        isVisible = !isVisible;
        tableDisplay.style.display = isVisible ? 'block' : 'none';
        toggleBtn.textContent = isVisible ? '📊 表構造を隠す' : '📊 復元された表構造を表示';
      });

      tableContainer.appendChild(toggleBtn);
      tableContainer.appendChild(tableDisplay);

      // transBoxの後に挿入
      const transBox = document.getElementById('transBox');
      transBox.parentNode.insertBefore(tableContainer, transBox.nextSibling);
    }
  }

  // ===== OCR情報表示 =====
  function showOCRInfo(note) {
    const ocrInfo = note.ocr_info || {};
    const method = ocrInfo.method || "unknown";
    const confidence = ocrInfo.confidence || 0;

    // OCR方法の表示名を決定
    let methodDisplay = "";
    let methodClass = "";

    if (method.includes("azure_computer_vision_layout_enhanced")) {
      methodDisplay = "🖋️ Azure Computer Vision (レイアウト保持)";
      methodClass = "ocr-azure";
    } else if (method.includes("azure_computer_vision_structured")) {
      methodDisplay = "🖋️ Azure Computer Vision (構造化)";
      methodClass = "ocr-azure";
    } else if (method.includes("azure")) {
      methodDisplay = "🖋️ Azure Computer Vision";
      methodClass = "ocr-azure";
    } else {
      methodDisplay = "❓ 不明";
      methodClass = "ocr-unknown";
    }

    // OCR情報エリアを更新
    let ocrInfoDiv = document.getElementById("ocr-info");
    if (!ocrInfoDiv) {
      ocrInfoDiv = document.createElement("div");
      ocrInfoDiv.id = "ocr-info";
      ocrInfoDiv.className = "ocr-info-panel";

      // 転写内容の前に挿入
      const transBox = document.getElementById("transBox");
      transBox.parentNode.insertBefore(ocrInfoDiv, transBox);
    }

    let infoHTML = `
      <div class="ocr-method ${methodClass}">
        <strong>OCR方法:</strong> ${methodDisplay}
        <span class="confidence">信頼度: ${(confidence * 100).toFixed(1)}%</span>
      </div>
    `;

    ocrInfoDiv.innerHTML = infoHTML;
  }

  // ===== ① OCR Upload =====
  btnUpload.addEventListener("click", async () => {
    if (imgInput.files.length === 0) {
      alert("ファイルを１つ以上選択してください");
      return;
    }
    noteStatus.textContent = "OCR 実行中...";

    // 画像も PDF も同じ FormData にまとめる
    const fd = new FormData();
    for (const f of imgInput.files) {
      if (f.type === "application/pdf" || f.name.toLowerCase().endsWith(".pdf")) {
        // PDF → 画像配列
        const pages = await pdfToImages(f);
        pages.forEach(png => fd.append("files", png));  
      } else {
        fd.append("files", f);
      }
    }

    try {
      const res = await apiFetch("/writer/note", { method: "POST", body: fd });
      if (!res.ok) throw new Error(await res.text());
      const data = await res.json();

      noteId    = data.note_id;
      notePages = data.notes;    // 複数ページ分を取得
      currentPage = 0;

      // ページナビを表示＆レンダリング
      pageNav.classList.remove("hidden");
      renderPage(0);

      noteSec.classList.remove("hidden");
      btnMakePaper.classList.remove("hidden");
      noteStatus.textContent = "転写結果を取得しました。必要なら編集してください。";
    } catch (err) {
      console.error(err);
      noteStatus.textContent = "OCR に失敗しました";
    }
  });

  // ===== ページ送り操作 =====
  prevBtn.addEventListener("click", () => {
    if (currentPage > 0) {
      currentPage--;
      renderPage(currentPage);
    }
  });
  nextBtn.addEventListener("click", () => {
    if (currentPage < notePages.length - 1) {
      currentPage++;
      renderPage(currentPage);
    }
  });

  // ===== 表示 / 編集モード切替 =====
  toggleEdit.addEventListener("click", () => {
    editMode = !editMode;
    transBox.readOnly = !editMode;
    toggleEdit.textContent = editMode ? "表示モードに戻す" : "編集モードにする";
    if (editMode) transBox.focus();
  });

  // ===== ② 論文生成 =====
  btnMakePaper.addEventListener("click", async () => {
    // まず、現在編集中のページの変更を notePages に反映
    notePages[currentPage]["転写内容"] = transBox.value;

    // 全ページ分をまとめて送信
    const payload = {
      note_id: noteId,
      edited_pages: notePages    // Array< note dict >
    };

    paperViewer.value = "論文生成中...";
    paperSec.classList.remove("hidden");
    try {
      const res = await apiFetch("/writer/paper", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(payload)
      });
      if (!res.ok) throw new Error(await res.text());
      const data = await res.json();
      paperId = data.paper_id;
      paperViewer.value = data.paper;
      currentPaper = data.paper; // 現在の論文を保存
    } catch (err) {
      console.error(err);
      paperViewer.value = "生成に失敗しました";
    } finally {
      // いずれにせよ、生成欄までスクロール
      paperSec.classList.remove("hidden");
      // 最下部までスクロール
      window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
    }
  });

  // ===== ③ リライト =====
  btnRewrite.addEventListener("click", async () => {
    const feedback = feedbackBox.value.trim();
    if (!feedback) {
      alert("フィードバックを入力してください");
      return;
    }
    revisedViewer.value = "リライト中...";
    revisedSec.classList.remove("hidden");
    try {
      const res = await apiFetch("/writer/paper/rewrite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          paper_id: paperId,
          feedback: feedback
        })
      });
      if (!res.ok) throw new Error(await res.text());
      const data = await res.json();
      currentPaper = data.revised_paper;
      revisedViewer.value = currentPaper;
      rewriteCounter = 1;
      rewriteCount.textContent = `（${rewriteCounter}回目）`;
    } catch (err) {
      console.error(err);
      revisedViewer.value = "リライトに失敗しました";
    } finally {
      // リライト欄までスクロール
      revisedSec.classList.remove("hidden");
      // 最下部までスクロール
      window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
    }
  });

  // ===== 次のリライト =====
  if (btnNextRewrite) {
    btnNextRewrite.addEventListener("click", async () => {
      if (rewriteCounter >= 3) {
        alert("リライトは3回までです");
        return;
      }
      
      const feedback = nextFeedbackBox.value.trim();
      if (!feedback) {
        alert("フィードバックを入力してください");
        return;
      }
      
      revisedViewer.value = "リライト中...";
      try {
        const res = await apiFetch("/writer/paper/rewrite", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            paper_id: paperId,
            feedback: feedback
          })
        });
        if (!res.ok) throw new Error(await res.text());
        const data = await res.json();
        currentPaper = data.revised_paper;
        revisedViewer.value = currentPaper;
        rewriteCounter++;
        rewriteCount.textContent = `（${rewriteCounter}回目）`;
        nextFeedbackBox.value = "";
      } catch (err) {
        console.error(err);
        revisedViewer.value = "リライトに失敗しました";
      } finally {
        // 最下部までスクロール
        window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
      }
    });
  }

  // ===== 手動修正モードへ =====
  if (btnManualEdit) {
    btnManualEdit.addEventListener("click", () => {
      manualEditBox.value = currentPaper;
      manualEditSec.classList.remove("hidden");
      // 最下部までスクロール
      window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
    });
  }

  // ===== テストページへの認証付きリンク =====
  window.openTestPage = function() {
    // 認証トークンを確認
    const token = localStorage.getItem('jwt') ||
                  localStorage.getItem('access_token') ||
                  localStorage.getItem('token');

    if (!token) {
      // 未認証の場合は警告メッセージを表示
      alert('このページにアクセスするには認証が必要です。\n\n先にログインしてからテストページにアクセスしてください。');
      return;
    }

    // 認証済みの場合はJWTトークンをURLパラメータで渡してテストページを開く
    const testUrl = `/static/test_citations.html?jwt=${encodeURIComponent(token)}`;
    window.open(testUrl, '_blank');
  };

  // ===== 文献の自動追加 =====
  if (btnAddCitations) {
    btnAddCitations.addEventListener("click", async () => {
      const paperText = manualEditBox.value.trim();
      if (!paperText) {
        alert("論文内容がありません");
        return;
      }

      // プログレス表示の初期化
      citedPaperViewer.value = "文献追加処理を開始しています...";
      citedPaperSec.classList.remove("hidden");
      btnAddCitations.disabled = true;

      // プログレス更新関数
      const updateProgress = (message) => {
        citedPaperViewer.value = message;
      };

      try {
        updateProgress("セクション抽出中...");

        // イントロダクションと考察のセクションを抽出
        const introSection = extractSection(paperText, "イントロダクション");
        const discussionSection = extractSection(paperText, "考察");

        if (!introSection && !discussionSection) {
          throw new Error("イントロダクションまたは考察セクションが見つかりません");
        }

        updateProgress("文献検索を開始しています...\n処理状況は下記に表示されます。");

        // 著者情報を取得
        const authorLastName = document.getElementById("authorLastName")?.value.trim() || "";
        const authorFirstName = document.getElementById("authorFirstName")?.value.trim() || "";
        const prioritizeAuthor = document.getElementById("prioritizeAuthor")?.checked || false;
        const requireHighImpact = document.getElementById("requireHighImpact")?.checked || false;

        // 進捗表示用のタイマー
        let progressCounter = 0;
        const progressMessages = [
          "📚 PubMedデータベースに接続中...",
          "🔍 キーワードを抽出中...",
          "📖 関連論文を検索中...",
          "🤖 AI評価を実行中...",
          "⚡ 最適な文献を選択中...",
          "📝 引用タグを生成中...",
          "🔗 論文テキストに統合中..."
        ];

        // 著者検索がある場合はメッセージを追加
        if (prioritizeAuthor && authorLastName) {
          progressMessages.unshift(`👤 ${authorLastName}氏の既存文献を検索中...`);
          progressMessages.splice(1, 0, `📚 ${authorLastName}氏の研究歴史を分析中...`);
          progressMessages.splice(2, 0, `✍️ 著者の過去研究を紹介する文章を生成中...`);
        }

        const progressTimer = setInterval(() => {
          const message = progressMessages[progressCounter % progressMessages.length];
          updateProgress(`文献検索実行中...\n\n${message}\n\n処理には10分以上かかる場合があります。`);
          progressCounter++;
        }, 3000);

        try {
          // 文献追加APIを呼び出し
          const res = await apiFetch("/writer/paper/add_citations", {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              paper_id: paperId,
              paper_text: paperText,
              intro_text: introSection,
              discussion_text: discussionSection,
              author_last_name: authorLastName,
              author_first_name: authorFirstName,
              prioritize_author: prioritizeAuthor,
              require_high_impact: requireHighImpact
            })
          });

          clearInterval(progressTimer);

          if (!res.ok) throw new Error(await res.text());

          updateProgress("結果を処理中...");
          const data = await res.json();
        
        citationData = data;
        currentPaper = data.cited_text;
        risUrl = data.ris_url;
        reportUrl = data.detail_json;

        // 完了メッセージを一時的に表示
        const refCount = data.references ? data.references.length : 0;
        updateProgress(`✅ 文献追加完了！\n\n📚 ${refCount}件の文献を追加しました\n📄 引用タグを論文テキストに統合しました\n📋 APA形式の文献リストを生成しました`);

        // 1.5秒後に結果を表示
        setTimeout(() => {
          // デフォルト表示（ユーザーの選択を尊重）
          updateCitationDisplay(false); // false = 強制しない
        }, 1500);

        } catch (apiError) {
          clearInterval(progressTimer);
          if (apiError.message && apiError.message.includes("408")) {
            updateProgress("⏰ 処理がタイムアウトしました（15分）\n\n論文の長さを短くするか、後でもう一度お試しください。");
          } else {
            throw apiError;
          }
        }

      } catch (err) {
        console.error(err);
        citedPaperViewer.value = "文献追加に失敗しました: " + err.message;
      } finally {
        btnAddCitations.disabled = false;
        // 最下部までスクロール
        window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
      }
    });
  }

  // ===== 引用表示形式の切り替え =====
  if (citationFormat) {
    citationFormat.addEventListener("change", updateCitationDisplay);
  }

  function updateCitationDisplay(forceNumeric = false) {
    if (!citationData) return;

    // 初回表示時のみデフォルトを番号表示に設定
    if (forceNumeric) {
      citationFormat.value = "numeric";
    }

    if (citationFormat.value === "endnote") {
      // Endnoteタグ形式
      citedPaperViewer.value = citationData.cited_text;
    } else {
      // 引用番号形式
      citedPaperViewer.value = citationData.numeric_text || convertToNumeric(citationData.cited_text);
    }
  }

  // Endnoteタグを引用番号に変換（バックエンドから提供されない場合）
  function convertToNumeric(text) {
    let counter = 1;
    const refMap = {};
    
    return text.replace(/\{([^}]+)\}/g, (match, tag) => {
      const tags = tag.split(';').map(t => t.trim());
      const nums = [];
      
      for (const t of tags) {
        if (!refMap[t]) {
          refMap[t] = counter++;
        }
        nums.push(refMap[t]);
      }
      
      return `[${nums.join(',')}]`;
    });
  }

  // ===== RISファイルのダウンロード =====
  if (btnDownloadRIS) {
    btnDownloadRIS.addEventListener("click", () => {
      if (!risUrl) {
        alert("RISファイルが利用できません");
        return;
      }
      
      // 新しいタブでRISファイルを開く
      window.open(risUrl, '_blank');
    });
  }

  // ===== 詳細レポートの表示/非表示 =====
  if (btnShowReport) {
    btnShowReport.addEventListener("click", async () => {
      if (!reportUrl) {
        alert("詳細レポートが利用できません");
        return;
      }
      
      if (reportSection.classList.contains("hidden")) {
        // 詳細レポートを表示
        try {
          const res = await fetch(reportUrl);
          if (!res.ok) throw new Error("レポートの取得に失敗しました");
          const data = await res.json();
          reportViewer.textContent = JSON.stringify(data, null, 2);
          reportSection.classList.remove("hidden");
          btnShowReport.textContent = "詳細レポートを隠す";
        } catch (err) {
          console.error(err);
          alert("レポートの取得に失敗しました: " + err.message);
        }
      } else {
        // 詳細レポートを隠す
        reportSection.classList.add("hidden");
        btnShowReport.textContent = "詳細レポートを表示";
      }
    });
  }

  // ===== セクション抽出ヘルパー関数 =====
  function extractSection(text, sectionName) {
    // セクション名のバリエーション
    const sectionVariants = {
      "イントロダクション": ["イントロダクション", "はじめに", "序論"],
      "考察": ["考察", "ディスカッション"]
    };
    
    // 検索するセクション名の配列
    const searchNames = sectionVariants[sectionName] || [sectionName];
    
    // 次のセクション名の配列
    const nextSections = ["材料と方法", "方法", "結果", "考察", "結論", "謝辞", "参考文献"];
    
    // 現在のセクションが「考察」の場合、「考察」の後に来るセクションだけを対象にする
    if (sectionName === "考察") {
      const index = nextSections.indexOf("考察");
      if (index !== -1) {
        nextSections.splice(0, index + 1);
      }
    }
    
    // セクションの開始位置を検索
    let sectionStart = -1;
    let foundSectionName = "";
    
    for (const name of searchNames) {
      const pos = text.indexOf(name);
      if (pos !== -1 && (sectionStart === -1 || pos < sectionStart)) {
        sectionStart = pos;
        foundSectionName = name;
      }
    }
    
    if (sectionStart === -1) {
      return null; // セクションが見つからない
    }
    
    // セクションの終了位置を検索
    let sectionEnd = text.length;
    
    for (const nextSection of nextSections) {
      if (nextSection === foundSectionName) continue;
      
      const pos = text.indexOf(nextSection, sectionStart + foundSectionName.length);
      if (pos !== -1 && pos < sectionEnd) {
        sectionEnd = pos;
      }
    }
    
    // セクション内容を抽出（セクション名を含む）
    return text.substring(sectionStart, sectionEnd).trim();
  }
  } // initializeApp()の終了
})();
