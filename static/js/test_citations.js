// 文献追加テスト用JavaScript

(function() {
  'use strict';

  // DOM要素の取得
  const paperTextInput = document.getElementById('paperTextInput');
  const authorLastName = document.getElementById('authorLastName');
  const authorFirstName = document.getElementById('authorFirstName');
  const prioritizeAuthor = document.getElementById('prioritizeAuthor');
  const requireHighImpact = document.getElementById('requireHighImpact');
  const btnTestCitations = document.getElementById('btnTestCitations');
  const btnCheckAuth = document.getElementById('btnCheckAuth');
  const progressArea = document.getElementById('progressArea');
  const resultSection = document.getElementById('resultSection');
  const resultViewer = document.getElementById('resultViewer');
  const citationFormat = document.getElementById('citationFormat');
  const btnDownloadRIS = document.getElementById('btnDownloadRIS');
  const btnShowReport = document.getElementById('btnShowReport');
  const reportSection = document.getElementById('reportSection');
  const reportViewer = document.getElementById('reportViewer');

  // グローバル変数
  let citationData = null;
  let risUrl = null;
  let reportUrl = null;

  // 初期化時に認証状態をチェック
  function checkAuthStatus() {
    const token = localStorage.getItem('access_token') ||
                  localStorage.getItem('jwt') ||
                  localStorage.getItem('token') ||
                  sessionStorage.getItem('access_token') ||
                  sessionStorage.getItem('jwt') ||
                  sessionStorage.getItem('token');

    if (!token) {
      progressArea.textContent = `❌ 認証エラー

このページは認証が必要です。以下の手順で認証してください：

1. メインページ（/static/writer.html）を開く
2. ログインする
3. 「文献追加テストページ」のリンクをクリック

または、直接ログインページにアクセス:`;

      // メインページへのリンクボタンを作成
      const mainPageButton = document.createElement('button');
      mainPageButton.textContent = 'メインページに移動してログイン';
      mainPageButton.style.cssText = 'margin: 10px 0; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;';
      mainPageButton.onclick = () => {
        window.location.href = '/static/writer.html';
      };

      progressArea.appendChild(document.createElement('br'));
      progressArea.appendChild(mainPageButton);

      btnTestCitations.disabled = true;
      btnTestCitations.textContent = '認証が必要です';
      return false;
    } else {
      progressArea.textContent = `✅ 認証OK (トークン: ${token.substring(0, 20)}...)

サンプルテキストを選択するか、論文テキストを入力して「文献追加を実行」ボタンをクリックしてください。`;

      btnTestCitations.disabled = false;
      btnTestCitations.textContent = '文献追加を実行';
      return true;
    }
  }

  // ページ読み込み時に認証チェック
  document.addEventListener('DOMContentLoaded', () => {
    checkAuthStatus();
  });

  // 認証状態再チェックボタン
  if (btnCheckAuth) {
    btnCheckAuth.addEventListener('click', () => {
      checkAuthStatus();
    });
  }

  // サンプルテキストの定義
  const sampleTexts = {
    covid: `イントロダクション
COVID-19は2019年末に中国で発生し、世界的なパンデミックを引き起こした。
SARS-CoV-2ウイルスによる感染症であり、多くの患者で重篤な症状を呈している。
特にサイトカインストームが重症化の主要因として注目されている。
従来の治療法では限界があり、新たな治療選択肢の開発が急務である。
本研究では、市販のど飴抽出液のサイトカインストーム抑制効果を検証した。

材料と方法
市販のど飴5種類を選定し、各々から水抽出液を調製した。
ヒト単球系細胞株THP-1を用いて、LPS刺激によるサイトカイン産生モデルを構築した。
各抽出液の細胞毒性をMTTアッセイで評価し、非毒性濃度を決定した。
IL-6、TNF-α、IL-1βの産生量をELISAで測定した。

結果
のど飴抽出液は濃度依存的にサイトカイン産生を抑制した。
特にカンゾウ含有製品で強い抑制効果が認められた。
細胞毒性は認められず、安全性が確認された。

考察
本研究の結果、のど飴抽出液にサイトカインストーム抑制効果が認められた。
これは従来の治療法に新たな選択肢を提供する可能性がある。
カンゾウに含まれるグリチルリチンの抗炎症作用が関与していると考えられる。
今後、臨床応用に向けた詳細な検討が必要である。`,

    cancer: `イントロダクション
がんは世界的な死因の上位を占める疾患であり、新たな治療法の開発が求められている。
近年、免疫チェックポイント阻害剤による免疫療法が注目されている。
PD-1/PD-L1経路の阻害により、T細胞の抗腫瘍免疫を活性化することが可能である。
しかし、すべての患者に効果があるわけではなく、バイオマーカーの同定が重要である。
本研究では、腫瘍組織のPD-L1発現と治療効果の関連を検討した。

材料と方法
非小細胞肺がん患者100例を対象とした。
免疫組織化学染色によりPD-L1発現を評価した。
PD-1阻害剤による治療効果をRECIST基準で判定した。
統計解析にはカイ二乗検定を用いた。

結果
PD-L1高発現群では奏効率が60%であったのに対し、低発現群では20%であった。
統計学的に有意な差が認められた（p<0.01）。
副作用の発現頻度に群間差は認められなかった。

考察
PD-L1発現は免疫チェックポイント阻害剤の効果予測因子として有用である。
高発現患者では積極的な治療適応が考慮される。
一方、低発現患者でも一定の効果が認められ、他の因子との組み合わせが重要である。
今後、より精密な患者選択法の確立が期待される。`,

    ai: `イントロダクション
人工知能（AI）技術の発展により、医療分野での応用が急速に進んでいる。
特に画像診断領域では、深層学習を用いた自動診断システムが開発されている。
胸部X線画像による肺炎診断は、救急医療において重要な課題である。
従来の診断では医師の経験に依存する部分が大きく、診断精度にばらつきがある。
本研究では、畳み込みニューラルネットワーク（CNN）を用いた肺炎自動診断システムを開発した。

材料と方法
胸部X線画像データセット10,000枚を用いて学習を行った。
ResNet-50をベースとしたCNNモデルを構築した。
データ拡張技術により学習データを増強した。
5分割交差検証により性能を評価した。
感度、特異度、AUCを算出した。

結果
開発したシステムの感度は95.2%、特異度は92.8%であった。
AUCは0.94と高い診断性能を示した。
処理時間は1画像あたり0.5秒であった。

考察
本システムは高い診断精度を達成し、臨床応用の可能性が示された。
迅速な診断により、早期治療開始が期待できる。
ただし、稀な疾患や複雑な症例では限界がある。
医師の最終判断を支援するツールとしての位置づけが適切である。`
  };

  // 認証情報の取得
  async function apiFetch(url, options = {}) {
    // 複数のトークンキーを試す
    let token = localStorage.getItem('access_token') ||
                localStorage.getItem('jwt') ||
                localStorage.getItem('token');

    if (!token) {
      // セッションストレージも確認
      token = sessionStorage.getItem('access_token') ||
              sessionStorage.getItem('jwt') ||
              sessionStorage.getItem('token');
    }

    if (!token) {
      console.error('Available localStorage keys:', Object.keys(localStorage));
      console.error('Available sessionStorage keys:', Object.keys(sessionStorage));
      throw new Error('認証が必要です。メインページでログインしてからテストページにアクセスしてください。');
    }

    console.log('Using token:', token.substring(0, 20) + '...');

    const defaultOptions = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    return fetch(url, { ...defaultOptions, ...options });
  }

  // サンプルテキストの読み込み
  window.loadSample = function(type) {
    if (sampleTexts[type]) {
      paperTextInput.value = sampleTexts[type];
      progressArea.textContent = `${type}サンプルを読み込みました。`;
    }
  };

  // テキストのクリア
  window.clearText = function() {
    paperTextInput.value = '';
    progressArea.textContent = 'テキストをクリアしました。';
  };

  // 進捗更新関数
  function updateProgress(message) {
    progressArea.textContent = message;
    progressArea.scrollTop = progressArea.scrollHeight;
  }

  // 文献追加実行
  if (btnTestCitations) {
    btnTestCitations.addEventListener('click', async () => {
      const paperText = paperTextInput.value.trim();
      if (!paperText) {
        alert('論文テキストを入力してください');
        return;
      }

      // プログレス表示の初期化
      updateProgress('文献追加処理を開始しています...');
      resultSection.style.display = 'none';
      btnTestCitations.disabled = true;

      try {
        updateProgress('セクション抽出中...');

        // イントロダクションと考察のセクションを抽出
        const introSection = extractSection(paperText, "イントロダクション");
        const discussionSection = extractSection(paperText, "考察");

        if (!introSection && !discussionSection) {
          throw new Error('イントロダクションまたは考察セクションが見つかりません');
        }

        updateProgress('文献検索を開始しています...\n処理状況は下記に表示されます。');

        // 著者情報を取得
        const authorLastNameValue = authorLastName?.value.trim() || "";
        const authorFirstNameValue = authorFirstName?.value.trim() || "";
        const prioritizeAuthorValue = prioritizeAuthor?.checked || false;
        const requireHighImpactValue = requireHighImpact?.checked || false;

        // 進捗表示用のタイマー
        let progressCounter = 0;
        const progressMessages = [
          "📚 PubMedデータベースに接続中...",
          "🔍 キーワードを抽出中...",
          "📖 関連論文を検索中...",
          "🤖 AI評価を実行中...",
          "⚡ 最適な文献を選択中...",
          "📝 引用タグを生成中...",
          "🔗 論文テキストに統合中..."
        ];
        
        // 著者検索がある場合はメッセージを追加
        if (prioritizeAuthorValue && authorLastNameValue) {
          progressMessages.unshift(`👤 ${authorLastNameValue}氏の既存文献を検索中...`);
          progressMessages.splice(1, 0, `📚 ${authorLastNameValue}氏の研究歴史を分析中...`);
          progressMessages.splice(2, 0, `✍️ 著者の過去研究を紹介する文章を生成中...`);
        }

        const progressTimer = setInterval(() => {
          const message = progressMessages[progressCounter % progressMessages.length];
          updateProgress(`文献検索実行中...\n\n${message}\n\n処理には10分以上かかる場合があります。`);
          progressCounter++;
        }, 3000);

        try {
          // 文献追加APIを呼び出し（ダミーのpaper_idを使用）
          const res = await apiFetch("/writer/paper/add_citations", {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              paper_id: "test_" + Date.now(), // テスト用のダミーID
              paper_text: paperText,
              intro_text: introSection,
              discussion_text: discussionSection,
              author_last_name: authorLastNameValue,
              author_first_name: authorFirstNameValue,
              prioritize_author: prioritizeAuthorValue,
              require_high_impact: requireHighImpactValue
            })
          });

          clearInterval(progressTimer);

          if (!res.ok) throw new Error(await res.text());

          updateProgress("結果を処理中...");
          const data = await res.json();
        
          citationData = data;
          risUrl = data.ris_url;
          reportUrl = data.detail_json;

          // 完了メッセージを一時的に表示
          const refCount = data.references ? data.references.length : 0;
          updateProgress(`✅ 文献追加完了！\n\n📚 ${refCount}件の文献を追加しました\n📄 引用タグを論文テキストに統合しました\n📋 APA形式の文献リストを生成しました`);

          // 1.5秒後に結果を表示
          setTimeout(() => {
            // デフォルト表示（ユーザーの選択を尊重）
            updateCitationDisplay(false);
            resultSection.style.display = 'block';
            resultSection.scrollIntoView({ behavior: "smooth" });
          }, 1500);

        } catch (apiError) {
          clearInterval(progressTimer);
          throw apiError;
        }

      } catch (err) {
        console.error(err);
        updateProgress("文献追加に失敗しました: " + err.message);
      } finally {
        btnTestCitations.disabled = false;
      }
    });
  }

  // 引用表示形式の切り替え
  if (citationFormat) {
    citationFormat.addEventListener("change", updateCitationDisplay);
  }

  function updateCitationDisplay(forceNumeric = false) {
    if (!citationData) return;

    // 初回表示時のみデフォルトを番号表示に設定
    if (forceNumeric) {
      citationFormat.value = "numeric";
    }

    if (citationFormat.value === "endnote") {
      // Endnoteタグ形式
      resultViewer.value = citationData.cited_text;
    } else {
      // 引用番号形式
      resultViewer.value = citationData.numeric_text || convertToNumeric(citationData.cited_text);
    }
  }

  // Endnoteタグを引用番号に変換
  function convertToNumeric(text) {
    let counter = 1;
    const refMap = {};
    
    return text.replace(/\{([^}]+)\}/g, (match, tag) => {
      const tags = tag.split(';').map(t => t.trim());
      const nums = [];
      
      for (const t of tags) {
        if (!refMap[t]) {
          refMap[t] = counter++;
        }
        nums.push(refMap[t]);
      }
      
      return `[${nums.join(',')}]`;
    });
  }

  // セクション抽出ヘルパー関数
  function extractSection(text, sectionName) {
    const sectionVariants = {
      "イントロダクション": ["イントロダクション", "はじめに", "序論"],
      "考察": ["考察", "ディスカッション"]
    };
    
    const searchNames = sectionVariants[sectionName] || [sectionName];
    const nextSections = ["材料と方法", "方法", "結果", "考察", "結論", "謝辞", "参考文献"];
    
    if (sectionName === "考察") {
      const index = nextSections.indexOf("考察");
      if (index !== -1) {
        nextSections.splice(0, index + 1);
      }
    }
    
    let sectionStart = -1;
    let foundSectionName = "";
    
    for (const name of searchNames) {
      const pos = text.indexOf(name);
      if (pos !== -1 && (sectionStart === -1 || pos < sectionStart)) {
        sectionStart = pos;
        foundSectionName = name;
      }
    }
    
    if (sectionStart === -1) {
      return null;
    }
    
    let sectionEnd = text.length;
    
    for (const nextSection of nextSections) {
      if (nextSection === foundSectionName) continue;
      
      const pos = text.indexOf(nextSection, sectionStart + foundSectionName.length);
      if (pos !== -1 && pos < sectionEnd) {
        sectionEnd = pos;
      }
    }
    
    return text.substring(sectionStart, sectionEnd).trim();
  }

  // RISファイルのダウンロード
  if (btnDownloadRIS) {
    btnDownloadRIS.addEventListener("click", () => {
      if (!risUrl) {
        alert("RISファイルが利用できません");
        return;
      }
      
      window.open(risUrl, '_blank');
    });
  }

  // 詳細レポートの表示/非表示
  if (btnShowReport) {
    btnShowReport.addEventListener("click", async () => {
      if (!reportUrl) {
        alert("詳細レポートが利用できません");
        return;
      }
      
      if (reportSection.classList.contains("hidden")) {
        try {
          const res = await fetch(reportUrl);
          if (!res.ok) throw new Error("レポートの取得に失敗しました");
          const data = await res.json();
          reportViewer.textContent = JSON.stringify(data, null, 2);
          reportSection.classList.remove("hidden");
          btnShowReport.textContent = "詳細レポートを隠す";
        } catch (err) {
          console.error(err);
          alert("レポートの取得に失敗しました: " + err.message);
        }
      } else {
        reportSection.classList.add("hidden");
        btnShowReport.textContent = "詳細レポートを表示";
      }
    });
  }

})();
