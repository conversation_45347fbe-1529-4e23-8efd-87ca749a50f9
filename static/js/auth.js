const API_ME       = "/auth/me";
const API_LOGOUT   = "/auth/logout";
const API_CHECKOUT = "/billing/checkout";
const GOOGLE_LOGIN = "/auth/google/login";

function $(id){return document.getElementById(id)}

/* ---------- 役割表示テキスト JP / EN ----------------------- */
const ROLE_LABEL_EN = ["guest","no subscription","subscribed"];
const ROLE_LABEL_JP = ["ゲスト","無料会員","有料会員"];
const JA = (navigator.language || "").toLowerCase().startsWith("ja");



/*───────────────────────────────
 1) ?token=... を localStorage へ
────────────────────────────────*/
function stashTokenFromQuery(){
  const p = new URLSearchParams(location.search);
  const token = p.get("token") || p.get("jwt");
  if(token){
    localStorage.setItem("jwt",token);
    p.delete("token");
    p.delete("jwt");
    history.replaceState(null,"",location.pathname);
  }
}

/*───────────────────────────────
 2) /auth/me でユーザー取得
────────────────────────────────*/
let userLoadPromise = null;

async function loadUser(){
  const token = localStorage.getItem("jwt");
  if(!token){
    window.currentUser = null;
    renderGuest();
    return;
  }

  try {
    const res = await fetch(API_ME,{
      headers:{Authorization:"Bearer "+token}
    });

    if(!res.ok){
      localStorage.removeItem("jwt");
      window.currentUser = null;
      renderGuest();
      return;
    }
    const u = await res.json();
    window.currentUser = u;
    renderUser(u);
  } catch (error) {
    console.error('loadUser error:', error);
    localStorage.removeItem("jwt");
    window.currentUser = null;
    renderGuest();
  }
}

// ユーザー情報の読み込み完了を待つ関数
export function waitForUser(){
  if(userLoadPromise) return userLoadPromise;

  userLoadPromise = new Promise((resolve) => {
    // 既にユーザー情報が読み込まれている場合
    if(window.currentUser !== undefined){
      resolve(window.currentUser);
      return;
    }

    let attempts = 0;
    const maxAttempts = 100; // 5秒でタイムアウト (50ms * 100)

    // loadUser()が実行されるまで待機してから、その完了を待つ
    const waitForLoadUser = () => {
      attempts++;
      if(window.currentUser !== undefined){
        resolve(window.currentUser);
      } else if(attempts >= maxAttempts) {
        console.warn('waitForUser: タイムアウトしました。ゲストユーザーとして処理します。');
        window.currentUser = null; // ゲストユーザーとして設定
        resolve(null);
      } else {
        setTimeout(waitForLoadUser, 50);
      }
    };

    // DOMContentLoadedが既に発火している場合は即座に開始
    if(document.readyState === 'loading'){
      document.addEventListener('DOMContentLoaded', waitForLoadUser);
    } else {
      // 少し待ってからloadUserの実行を待つ
      setTimeout(waitForLoadUser, 100);
    }
  });

  return userLoadPromise;
}



/*───────────────────────────────
 3) UI 切替
────────────────────────────────*/
function renderGuest(){
  $("userName").innerHTML =
      '<button id="loginBtn" style="padding:0 8px">Login</button>';
  if($("slash"))    $("slash").style.display = "none";
  if($("userRole")) $("userRole").textContent = "guest";
  $("loginBtn").onclick = () => location.href = GOOGLE_LOGIN;

  if($("hits"))      $("hits").innerHTML = '<option>10</option>';
  if($("detailBtn")) $("detailBtn").hidden = true;
}

function renderUser(u){
  if($("slash"))    $("slash").style.display = "inline";
  if($("userName")) $("userName").textContent = u.email || u.username || "user";

  const roleTxt = (JA?ROLE_LABEL_JP:ROLE_LABEL_EN)[u.role ?? 0];
  if($("userRole")) $("userRole").textContent = roleTxt;

  const paid = u.role === 2;
  if($("hits")){
    $("hits").innerHTML = paid
      ? '<option>10</option><option>50</option><option>100</option>'
      : '<option>10</option>';
  }
  if($("detailBtn")) $("detailBtn").hidden = !paid;

  /* サブスク終了日ツールチップ */
  if (u.subscription_end){
    const d = u.subscription_end.slice(0,10);
    $("userBox").title = JA ? `契約終了日: ${d}` : `Subscription ends: ${d}`;
  }else{
    $("userBox").removeAttribute("title");
  }
}

/*───────────────────────────────
 4) メニュー / ログアウト / Checkout
────────────────────────────────*/
export function toggleMenu(){
  const m = $("menu");
  if(m) m.hidden = !m.hidden;
}

export async function logout(){
  console.log('logout function called'); // デバッグ用
  try {
    await fetch(API_LOGOUT,{method:"POST"});
    localStorage.removeItem("jwt");
    location.reload();
  } catch (error) {
    console.error('logout error:', error);
    // エラーが発生してもローカルストレージをクリアしてリロード
    localStorage.removeItem("jwt");
    location.reload();
  }
}

export async function buy(){
  const token = localStorage.getItem("jwt");
  if(!token){ alert("Please log in"); return; }

  const r = await fetch(API_CHECKOUT,{
    method:"POST",
    headers:{Authorization:"Bearer "+token}
  });

  if(r.ok){
    const {url} = await r.json();
    location.href = url;           // Stripe Checkout
    return;
  }

  /* ---- エラーハンドリング ---- */
  let msg = "Checkout failed";
  try{
    const err = await r.json();
    if(err.detail === "already_subscribed"){
      msg = JA ? "すでに購入済みです" : "You are already subscribed";
    }
  }catch(_){
    /* JSON でない場合はそのまま */
  }
  Toastify({text:msg,duration:3000,
            style:{background:"#d9534f"}}).showToast();
}

/*───────────────────────────────
 5) 初期化
────────────────────────────────*/
// HTMLのonclick属性で使用するためにグローバルに公開（即座に実行）
window.toggleMenu = toggleMenu;
window.logout = logout;
window.buy = buy;

// デバッグ用：関数が正しく公開されているか確認
console.log('Auth functions exported to window:', {
  toggleMenu: typeof window.toggleMenu,
  logout: typeof window.logout,
  buy: typeof window.buy
});

document.addEventListener("DOMContentLoaded",()=>{
  stashTokenFromQuery();
  loadUser();
  document.querySelectorAll('.lang-ja').forEach(el => el.style.display = JA ? 'block' : 'none');
  document.querySelectorAll('.lang-en').forEach(el => el.style.display = JA ? 'none' : 'block');
});



/*───────────────────────────────
 6) subscription helper
────────────────────────────────*/
/**
 * true  … 有料会員
 * false … 無料/ゲスト
 */
export function isPaidUser(){
  return window.currentUser && window.currentUser.role === 2;
}

