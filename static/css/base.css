/* -- Reset minimal -- */
*{box-sizing:border-box;margin:0;padding:0}
/* -- Theme -- */
:root{
  --bg:#f4f6f8;--fg:#202124;--border:#c9ced3;
  --accent:#168aad;--card:#fff;
}
@media(prefers-color-scheme:dark){
  :root{--bg:#262a2f;--fg:#e0e3e7;--border:#3b4046;
        --accent:#26a4c4;--card:#31363c;}
}

body {
  margin: 0;           
  background: var(--bg);
  color: var(--fg);
}
/* -- Header -- */
.topbar{width:100%;
  display:flex;justify-content:space-between;align-items:center;
        padding:12px 24px;background:var(--accent);color:#fff;
      position:relative; z-index: 1000;}
      
.logo{font-size:22px;}
.logo span{color:#fff;font-weight:300}  
.logo-link{color:#fff;text-decoration:none}
.logo-link:hover{text-decoration:underline}

.badge{cursor:pointer;position:relative;}
.badge:hover #menu{display:block;}
.badge #menu{position:absolute;right:0;top:160%;background:var(--card);
             border:1px solid var(--border);border-radius:6px;
             min-width:140px;box-shadow:0 4px 12px rgba(0,0,0,.15);
             z-index:9999 ;}
.badge #menu a{display:block;padding:8px 14px;text-decoration:none;
               color:var(--fg);border-bottom:1px solid var(--border);}
.badge #menu a:last-child{border-bottom:none;}
.badge #menu a:hover{background:var(--bg);}

/* content 内だけ幅制限 */
.content {
  max-width: 800px;
  margin: 2em auto;
  padding: 0 1em;
}

/* -- Grid of Apps -- */
main.grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));
          gap:24px;padding:32px;}
.card{background:var(--card);border:1px solid var(--border);
      border-radius:10px;padding:26px;cursor:pointer;transition:.2s;}
.card:hover{transform:translateY(-4px);box-shadow:0 4px 14px rgba(0,0,0,.12);}
.card.disabled{opacity:.4;cursor:default;transform:none;box-shadow:none;}

body.page{
  display:flex;
  flex-direction:column;
  min-height:100vh;      /* 画面の高さいっぱい */
  margin:0;
}
body.page > main{
  flex:1 0 auto;         /* 余白を全部 main が取る */
}
footer.site-footer{
  flex-shrink:0;
  background:#f8f9fa;
  border-top:1px solid #ccc;
  text-align:center;
  font-size:.9rem;
  padding:.8rem 0;
}
footer.site-footer a{color:#666;text-decoration:none}