/* writer.css */

/* 全体のスタイル */
body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* セクションのスタイル */
section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  background-color: #f9f9f9;
}

/* 見出しのスタイル */
h2 {
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
  margin-top: 0;
}

h3 {
  color: #34495e;
  margin-top: 20px;
}

/* テキストエリアのスタイル - 横方向のリサイズも可能に */
textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: both; /* 縦横両方向にリサイズ可能 */
  min-width: 300px; /* 最小幅を設定 */
  min-height: 100px; /* 最小高さを設定 */
}

/* OCR結果表示用の等幅フォント */
#transBox {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  white-space: pre-wrap; /* 改行と空白を保持 */
  overflow-wrap: break-word; /* 長い行の折り返し */
  tab-size: 4; /* タブサイズを4に設定 */
}

.paper-area {
  height: 400px;
  font-family: 'Georgia', serif;
  line-height: 1.8;
  resize: both; /* 縦横両方向にリサイズ可能 */
}

/* 引用付き論文表示用（表構造保持） */
#citedPaperViewer {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  background-color: #f8f9fa;
}

/* ボタンのスタイル */
button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 10px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #2980b9;
}

button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

/* ファイル入力のスタイル */
input[type="file"] {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px dashed #3498db;
  border-radius: 4px;
  width: 100%;
}

/* ステータスメッセージのスタイル */
#noteStatus {
  font-style: italic;
  color: #7f8c8d;
}

/* 質問リストのスタイル - 転写内容の下に表示されるように調整 */
#qArea {
  background-color: #f0f7fb;
  border-left: 5px solid #3498db;
  padding: 15px;
  margin: 15px 0;
  border-radius: 4px;
}

#questionList {
  margin: 0;
  padding-left: 20px;
}

#questionList li {
  margin-bottom: 8px;
  line-height: 1.4;
}

/* ページナビゲーションのスタイル */
#pageNav {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 15px 0;
}

#pageIndicator {
  font-weight: bold;
  margin: 0 15px;
}

/* OCR情報パネルのスタイル */
.ocr-info-panel {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  font-size: 13px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ocr-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 500;
}

.ocr-google {
  background-color: #e8f5e8;
  border-left: 4px solid #28a745;
  color: #155724;
}

.ocr-azure {
  background-color: #f0f8ff;
  border-left: 4px solid #0078d4;
  color: #003d6b;
}

.ocr-openai {
  background-color: #e8f4fd;
  border-left: 4px solid #007bff;
  color: #004085;
}

.ocr-hybrid {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
  color: #856404;
}

.ocr-unknown {
  background-color: #f8d7da;
  border-left: 4px solid #dc3545;
  color: #721c24;
}

.confidence {
  font-size: 12px;
  font-weight: normal;
  opacity: 0.8;
}

/* 引用関連のスタイル */
.display-options, .download-options {
  margin: 15px 0;
  display: flex;
  gap: 10px;
  align-items: center;
}

#reportViewer {
  max-height: 300px;
  overflow: auto;
  background: #f5f5f5;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.3;
  white-space: pre-wrap;
  resize: both; /* 縦横両方向にリサイズ可能 */
}

/* 表構造表示用のスタイル */
.table-structure-display {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.2;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
  margin: 10px 0;
  white-space: pre;
  overflow-x: auto;
  overflow-y: auto;
  max-height: 400px;
}

/* 復元された表のスタイル */
.reconstructed-table {
  background-color: #e8f5e8;
  border-left: 4px solid #28a745;
  margin: 15px 0;
  padding: 15px;
  border-radius: 4px;
}

.reconstructed-table h4 {
  margin: 0 0 10px 0;
  color: #155724;
  font-size: 14px;
  font-weight: bold;
}

.reconstructed-table pre {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.3;
  margin: 0;
  white-space: pre;
  overflow-x: auto;
  background-color: #ffffff;
  padding: 10px;
  border-radius: 3px;
  border: 1px solid #c3e6cb;
}

/* レスポンシブデザイン */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }
  
  section {
    padding: 15px;
  }
  
  .paper-area {
    height: 300px;
  }
  
  textarea {
    min-width: 250px; /* モバイルでの最小幅を調整 */
  }
}
