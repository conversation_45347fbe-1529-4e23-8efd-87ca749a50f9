:root{--bg:#f4f6f8;--fg:#202124;--border:#c9ced3;--accent:#168aad;--card:#fff;}
@media (prefers-color-scheme:dark){:root{--bg:#262a2f;--fg:#e0e3e7;--border:#3b4046;--accent:#26a4c4;--card:#31363c;}}
body{background:var(--bg);color:var(--fg);font-family:sans-serif;margin:32px;max-width:900px;}
textarea{width:100%;height:120px;font-size:15px;background:var(--card);color:var(--fg);
         border:1px solid var(--border);border-radius:4px;padding:8px;}
select{margin-right:12px;padding:4px;border-radius:4px;}
button{background:var(--accent);color:#fff;border:none;border-radius:4px;
       padding:6px 20px;font-size:14px;cursor:pointer;}
button[disabled]{opacity:.5;cursor:default;}
progress{width:100%;height:14px;border:none;background:var(--border);
         border-radius:6px;overflow:hidden;}
progress::-webkit-progress-value,progress::-moz-progress-bar{background:var(--accent);}
pre{background:var(--card);border:1px solid var(--border);padding:12px;border-radius:4px;
    white-space:pre-wrap;word-break:break-all;}
#log{max-height:240px;overflow-y:auto;}
.copyBtn{margin-left:8px;font-size:12px;padding:2px 8px;}
.dlBtn{background:var(--accent);color:#fff;border:none;border-radius:4px;
       padding:6px 18px;font-size:14px;text-decoration:none;display:inline-block;cursor:pointer;}
#cost{float:right;font-size:13px;margin-top:-6px;}

/* --- Explanation section------------------------- */
.info-box{
  background:var(--card);
  border:1px solid var(--border);
  border-radius:8px;
  padding:24px;
  margin-bottom:32px;
  box-shadow:0 2px 6px rgba(0,0,0,.08);
}


/* i18n block ─ 初期は非表示、JS で片方だけ表示 */
.lang-ja, .lang-en {display:none;}
