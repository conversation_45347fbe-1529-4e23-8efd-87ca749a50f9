/* todos.css */

h1, h2 {
  margin-top: 1.5em;
}

button {
  margin-right: 8px;
  padding: 0.5em 1em;
}

/* 既存のすべての ul に対するデフォルトスタイル */
ul {
  list-style-type: disc;
  margin-left: 1.5em;
}

/* ──────────────────────────────────────────── */
/* #todoList 用：箇条書き記号を消し、ぶら下げインデント */
/* ──────────────────────────────────────────── */
ul#todoList {
  list-style: none;    /* “・”を消す */
  margin: 0;           /* 必要なら余白を調整 */
  padding: 0;
}

/* li を flex コンテナにして、アイコン＋本文を横並びに */
ul#todoList li.todo-item {
  display: flex;
  align-items: flex-start;   /* アイコンとテキストの上端を揃える */
  margin-bottom: 0.5em;      /* 各項目の下に間隔 */
  line-height: 1.4;          /* 行間はお好みで */
}

/* 優先度アイコンやメールアイコン部分 */
ul#todoList li.todo-item .todo-icon {
  flex-shrink: 0;        /* 横幅を固定 */
  width: 1.5em;          /* アイコンの実寸に合わせて調整 */
  text-align: center;
  margin-right: 0.5em;   /* アイコンと本文の間の余白 */
}

/* カテゴリタグ部分 */
ul#todoList li.todo-item .tag {
  flex-shrink: 0;
  margin-right: 0.5em;   /* アイコンと本文の間の余白 */
}

/* 実際のテキスト部分。ここが折返し後の開始位置になる */
ul#todoList li.todo-item .todo-text {
  flex: 1;                   /* 残り幅を全部使う */
  white-space: pre-wrap;     /* 改行を有効に */
  word-break: break-word;    /* 長い単語の途中で折返しOK */
}

/* リンク（もし li 内に a を使うなら） */
ul#todoList li.todo-item .todo-text a {
  text-decoration: none;
  color: inherit;
}

/* 既存の a:hover や button, spinner, tag 色設定はそのまま下に */
a {
  color: #0066cc;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 優先度アイコンに余白 （#todoList とは別の汎用設定があるならそのまま）*/
ul#todoList li {
  /* line-height: 1.4; すでに上で指定済み */
}

.todo-item {
  /* flex化はこちらでは使わず ul#todoList li.todo-item で行っています */
}

.link-button {
  margin-right: 8px;
}

.spinner {
  display: none;
  margin-left: 8px;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0%   { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* タグバッジ */
.tag {
  display: inline-block;
  padding: 0.2em 0.5em;
  margin-right: 0.5em;
  border-radius: 4px;
  font-size: 0.8em;
  color: #fff;
}
.tag-実験   { background: #6c5ce7; }
.tag-会議   { background: #0984e3; }
.tag-教育   { background: #00b894; }
.tag-管理   { background: #fdcb6e; color: #000; }
.tag-その他 { background: #d63031; }



/* トピック関与者リスト */
.participants-list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.participants-list li {
  display: inline-block;
  margin: .2em;
  padding: .3em .6em;
  border-radius: 4px;
  /* 色は親要素の color／background-color を継承 */
}

/* フォーム内スピナー */
.spinner {
  display: none;
  margin-left: .5em;
  width: 1em;
  height: 1em;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin .8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}




/* hidden クラス */
.task--hidden {
  opacity: 0.5;
  text-decoration: line-through;
}
.hide-btn {
  margin-left: 0.5em;
  font-size: 0.8em;
}


/* メインコンテンツの幅を広く設定 */
.content {
  max-width: 1200px;   /* 最大 1200px まで広げる */
  width: 90%;          /* 画面幅の 90% を使う */
  margin: 0 auto;      /* 中央寄せ */
  padding: 1rem;       /* 余白を少し追加 */
}

/* 参与者抽出パネルなどの container も合わせて広げる */
.container {
  max-width: 1200px;
  width: 90%;
  margin: 2rem auto;
}

/* タスク一覧のリストも横に広がるようにする */
#todoList,
#eventList,
#schedList {
  max-width: 100%;
  overflow-x: auto;    /* 横に長い場合スクロール可能に */
}
