<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文献追加テスト - The Writer</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <script>
    // JWT処理（メインページと同じ）
    (()=>{const p=new URLSearchParams(location.search);const j=p.get('jwt');
      if(j){localStorage.setItem('jwt',j);
        history.replaceState({},'',location.pathname+location.hash);}})();
  </script>
  <style>
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .test-section {
      background: #f9f9f9;
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      border: 1px solid #ddd;
    }
    .paper-area {
      width: 100%;
      min-height: 300px;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.5;
    }
    .author-info-section {
      background: #f0f8ff;
      padding: 15px;
      margin: 15px 0;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .author-inputs {
      margin: 10px 0;
    }
    .author-inputs label {
      display: inline-block;
      width: 120px;
      margin-right: 10px;
    }
    .author-inputs input {
      width: 200px;
      padding: 5px;
      margin-right: 15px;
      margin-bottom: 10px;
    }
    .citation-options {
      margin: 10px 0;
    }
    .citation-options label {
      display: block;
      margin: 5px 0;
    }
    .progress-area {
      background: #f5f5f5;
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
      border: 1px solid #ddd;
      min-height: 100px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    .display-options {
      margin: 10px 0;
    }
    .download-options {
      margin: 10px 0;
    }
    .download-options button {
      margin-right: 10px;
    }
    #reportSection {
      margin-top: 20px;
    }
    #reportViewer {
      max-height: 300px;
      overflow: auto;
      background: #f5f5f5;
      padding: 10px;
      border: 1px solid #ddd;
    }
    .sample-buttons {
      margin: 10px 0;
    }
    .sample-buttons button {
      margin-right: 10px;
      margin-bottom: 5px;
      padding: 5px 10px;
      background: #e7f3ff;
      border: 1px solid #b3d9ff;
      border-radius: 4px;
      cursor: pointer;
    }
    .sample-buttons button:hover {
      background: #d0ebff;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>文献追加テスト - The Writer</h1>
    <p>論文テキストを直接入力して文献追加機能をテストできます。</p>

    <!-- サンプルテキスト選択 -->
    <section class="test-section">
      <h2>サンプルテキスト</h2>
      <div class="sample-buttons">
        <button onclick="loadSample('covid')">COVID-19研究サンプル</button>
        <button onclick="loadSample('cancer')">がん研究サンプル</button>
        <button onclick="loadSample('ai')">AI研究サンプル</button>
        <button onclick="clearText()">クリア</button>
      </div>
    </section>

    <!-- 論文テキスト入力 -->
    <section class="test-section">
      <h2>論文テキスト入力</h2>
      <textarea id="paperTextInput" class="paper-area" placeholder="論文テキストを入力してください。イントロダクションと考察セクションを含めてください。

例：
イントロダクション
COVID-19は世界的なパンデミックを引き起こし、多くの患者で重篤な症状を呈している。
特にサイトカインストームが重症化の主要因として注目されている。
本研究では、市販のど飴抽出液のサイトカインストーム抑制効果を検証した。

材料と方法
...

結果
...

考察
本研究の結果、のど飴抽出液にサイトカインストーム抑制効果が認められた。
これは従来の治療法に新たな選択肢を提供する可能性がある。
今後、臨床応用に向けた検討が必要である。"></textarea>
    </section>

    <!-- 著者情報入力 -->
    <section class="test-section">
      <div class="author-info-section">
        <h3>著者情報（オプション）</h3>
        <p>著者の既存文献を優先的に引用したい場合は、以下に入力してください：</p>
        <div class="author-inputs">
          <label>姓（Last Name）：</label>
          <input type="text" id="authorLastName" placeholder="例: Yamada">
          <label>名（First Name）：</label>
          <input type="text" id="authorFirstName" placeholder="例: Taro">
        </div>
        <div class="citation-options">
          <label>
            <input type="checkbox" id="prioritizeAuthor" checked>
            著者の既存文献を優先的に引用する
          </label>
          <label>
            <input type="checkbox" id="requireHighImpact">
            高インパクトファクター文献を優先（IF≥5）
          </label>
        </div>
      </div>
    </section>

    <!-- 文献追加実行 -->
    <section class="test-section">
      <h2>文献追加実行</h2>
      <div style="margin-bottom: 15px;">
        <button id="btnTestCitations" style="padding: 10px 20px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">文献追加を実行</button>
        <button id="btnCheckAuth" style="padding: 10px 15px; font-size: 14px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">認証状態を再チェック</button>
      </div>

      <h3>処理状況</h3>
      <div id="progressArea" class="progress-area">ここに処理状況が表示されます...</div>
    </section>

    <!-- 結果表示 -->
    <section id="resultSection" class="test-section" style="display: none;">
      <h2>文献追加結果</h2>
      
      <div class="display-options">
        <label>表示形式：</label>
        <select id="citationFormat">
          <option value="endnote" selected>Endnoteタグ</option>
          <option value="numeric">引用番号</option>
        </select>
      </div>
      
      <textarea id="resultViewer" class="paper-area" readonly></textarea>
      
      <div class="download-options">
        <button id="btnDownloadRIS">RISファイルをダウンロード</button>
        <button id="btnShowReport">詳細レポートを表示</button>
      </div>
      
      <div id="reportSection" class="hidden">
        <h3>引用詳細レポート</h3>
        <pre id="reportViewer"></pre>
      </div>
    </section>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script type="module" src="/static/js/auth.js"></script>
  <script type="module" src="/static/js/test_citations.js"></script>
</body>
</html>
