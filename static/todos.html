<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>My Task Manager</title>
  <link rel="stylesheet" href="/css/base.css">
  <link rel="stylesheet" href="/css/todos.css" />
  <script>
    (()=>{
      const p = new URLSearchParams(location.search);
      const j = p.get('jwt');
      if (j) {
        localStorage.setItem('jwt', j);
        history.replaceState({}, '', location.pathname + location.hash);
      }
    })();
  </script>
</head>
<body>

  <header class="topbar">
    <h1 class="logo">
      <a href="/" class="logo-link">MyAI<span>Agents</span></a>
    </h1>

    <!-- ★ userBox の中身は auth.js が書き換えるので構造はそのまま -->
    <div id="userBox" class="badge" onclick="toggleMenu()">
      <span id="userName">guest</span>
      <span id="slash"> / </span>
      <span id="userRole">guest</span>
      <div id="menu" hidden>
        <a href="#" onclick="buy();return false;">Buy Credits</a>
        <a href="/profile.html">Profile</a>
        <a href="#" onclick="logout();return false;">Logout</a>
      </div>
    </div>
  </header>

  <div class="content">
    <h1>My Task Manager</h1>

    <div id="btns">
      <button id="connectGmailBtn" style="display:none">Connect Gmail</button>
      <button id="connectCalBtn"   style="display:none">Connect Calendar</button>
      <button id="discGmailBtn"    style="display:none">Disconnect Gmail</button>
      <button id="discCalBtn"      style="display:none">Disconnect Calendar</button>
      
      
    </div>

    <div id="status">
    Gmail: <span id="gmailStatus"></span>
    Calendar: <span id="calStatus"></span>
    </div>

    <p><a href="/privacy.html" target="_blank">Privacy Policy</a></p>

    
      <div id="filter">
        <h2>Extracted ToDos</h2>
        <!-- 非表示タスク表示トグル -->
         <label style="margin-left:1em;">
         <input type="checkbox" id="showHiddenCheckbox">
         Show hidden tasks
        </label>

        <section id="categoryManager">
          <h3>Task Category  <input type="text" id="newCategoryName" placeholder="New category">
          <button id="addCategoryBtn">Add</button>
          </h3>

          <div id="defaultCats" style="display:none; margin-bottom:1em;">
              <p>既定カテゴリを追加:</p>
              <div id="defaultCatButtons"></div>
            </div>
          <ul id="categoryList"></ul>
        </section>


      </div>

    <!-- Extracted tasks will be shown here. -->
    <div id="todoListHeader">
      <h3>ToDo List</h3>
        <label>Filter by category:
          <select id="categoryFilter">
            <option value="">All</option>
            <option value="実験">実験</option>
            <option value="会議">会議</option>
            <option value="教育">教育</option>
            <option value="管理">管理</option>
            <option value="その他">その他</option>
          </select>
        </label>
      <button id="loadBtn" style="display:none">
        <br>
        <span id="loadSpinner" class="spinner" style="display:none">🔄</span>
        Load Tasks
      </button>

    <ul id="todoList"></ul>     
    <!-- フッター：追加のメッセージ／ボタンをここに表示 -->
    <div id="todoListFooter"></div>


    <div class="container">
        <h1>Topic to People</h1>

        <div class="panel">
          <div class="panel-header">Input Topic (JP/EN)</div>
          <form id="topicForm">
            <input type="text" id="topicInput" placeholder="Example: Alzhimer's disease" required>
            <button type="submit"> GO </button>
            <span id="topicSpinner" class="spinner"></span>
          </form>
        </div>

        <div class="panel">
          <div class="panel-header"> Relevant People</div>
          <ul id="participantsList">
            <!-- <li>山田 太郎</li> などが JS で入る -->
          </ul>
          <div id="noResult" style="display:none; color:#666;"> No results yet...</div>
        </div>
    </div> 

    <h2>Upcoming Events</h2>
    <ul id="eventList"></ul>

    <h2>Proposed Schedule</h2>
    <ul id="schedList"></ul>
  </div>  

  <!-- ★ Toastify は auth.js で使うため読み込み -->
  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script type="module" src="/static/js/auth.js"></script>
  <script src="/static/js/todos.js" defer></script>

</body>
</html>