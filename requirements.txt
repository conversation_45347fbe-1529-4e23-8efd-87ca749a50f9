# ── FastAPI / インフラ
fastapi>=0.111
uvicorn[standard]>=0.29
starlette>=0.32
sse-starlette>=1.8
aiofiles>=0.8

psycopg2-binary>=2.9

# ── 認証 / DB
fastapi-users[sqlalchemy2]>=12.2
fastapi-users-db-sqlalchemy>=3.0
sqlalchemy>=2.0
aiosqlite>=0.19         # SQLite async ドライバ（開発用）
asyncpg>=0.29           # PostgreSQL async ドライバ（本番用）
passlib[bcrypt]>=1.7
python-jose[cryptography]>=3.3
alembic>=1.12
greenlet>=2.0

# ── Google OAuth2
authlib>=1.3

# ── Gmail API を叩くのに必要
google-auth>=2.0.0
google-auth-oauthlib>=0.4.0
google-api-python-client>=2.0.0

# ── Google Cloud Vision API (OCR)
google-cloud-vision>=3.4.0

# ── Stripe 決済
stripe>=9.8

# ── OpenAI / LLM
openai>=1.14
langchain-openai>=0.1.3
biopython>=1.83

# ── ファイルアップロード
python-multipart>=0.0.5

# ── 補助ユーティリティ
janus>=1.0
python-dotenv>=1.0
itsdangerous>=2.1

# for fuzzy‐matching
rapidfuzz>=2.0.0

# PDFの画像化
pdf2image

# 画像処理 (OCR用)
Pillow>=10.0.0