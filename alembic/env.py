from logging.config import fileConfig
import os
import uuid                     # autogenerate で型推論に使われる場合がある

from sqlalchemy import engine_from_config, pool
from alembic import context

# すべてのモデルを import して metadata に登録
from agent.models import Base, User, Credit, Payment, Subscription   # noqa: F401

# ────────────────────────────────────────────────
# 1. Alembic Config
# ────────────────────────────────────────────────
config = context.config

# ────────────────────────────────────────────────
# 2. データベース URL を決定
#    - 環境変数 DATABASE_URL > alembic.ini
#    - 無ければローカル開発用に SQLite フォールバック
# ────────────────────────────────────────────────
target_url = (
    os.getenv("DATABASE_URL")
    or config.get_main_option("sqlalchemy.url")
    or "sqlite+aiosqlite:///./db.sqlite3"
)

# async ドライバの場合は同期ドライバに置換（Alembic は同期エンジン）
if target_url.startswith("postgresql+asyncpg"):
    target_url = target_url.replace("postgresql+asyncpg", "postgresql+psycopg2", 1)

# Alembic へ反映
config.set_main_option("sqlalchemy.url", target_url)

# ────────────────────────────────────────────────
# 3. ログ設定 & MetaData
# ────────────────────────────────────────────────
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = Base.metadata

# ────────────────────────────────────────────────
# 4. オフライン / オンライン実行
# ────────────────────────────────────────────────
def run_migrations_offline() -> None:
    """Run migrations without DB connection."""
    context.configure(
        url=target_url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )
    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations with a real DB connection."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )
    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)
        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()