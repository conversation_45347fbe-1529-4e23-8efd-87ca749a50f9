
User:
Gmail agentですが、終わったタスクの表示を手動で機能が欲しいです。
あと、引用文献に比べると、こちらはタスクを早く知りたいので、もう少しスピード感が欲しいところです。

I:
直します。
終わったタスクの表示を手動で消す機能ですね？
スピードは、並列処理でかなり改善すると思います。

User:
そうです。消したタスクについては次にロードした際には表示されないとより良いですが、消したタスクについて新しく来たメールにより別途対応が必要になった場合との線引きが困難でしょうか。
これは難しいかもですが、対応に必要な想定時間なんか出るとより良いですね。
あとは、送信元のアドレスによって重み付けを変えるとかでしょうか。
学内外（施設内外）の仕事を切り分ける機能なども、優先順位を判断するうえでは使えるかもしれません




User:
メールの履歴に基づいて、あるトピックに関与している人の一覧を欲しい。
