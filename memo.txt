以下、main.py＋pipeline.pyで実現しているマルチエージェントAI文献検索システムの全体仕様をまとめます。

――――――――――――――――――――――

全体構成 ―――――――――――――――――――――― ・フロントエンド（static 配下の HTML/JS） ←→ FastAPI サーバ (main.py) ・文献検索／スコアリング／選定のコアロジック ←→ pipeline.py
フロー：
ユーザーがブラウザから文章を送信
↓
FastAPI がパイプライン起動（同期 or SSE/ジョブ）
↓
pipeline.py:run_pipeline が
　① 文分割
　② キーワード抽出
　③ PubMed 検索 → 論文詳細取得
　④ LLM によるスコア付け
　⑤ 上位文献の選択
　⑥ 引用タグ挿入＋RIS／レポート生成
↓
結果（引用付きテキスト／RIS URL／JSONレポート）を返す
↓
フロントエンドが結果を描画

――――――――――――――――――――――
2. main.py の役割
――――――――――――――――――――――

アプリケーション定義＋ミドルウェア

FastAPI インスタンス生成
セッション管理（SessionMiddleware）
ユーザー認証ルーター（Google OAuth、JWT、FastAPI-Users）
課金ルーター（Stripe Checkout）
ジョブ管理（非同期進捗通知用）

jobs: Dict[job_id→{queue,done,result,stamp}] をグローバルに保持
TTL（JOB_TTL_SEC）で古いジョブを自動削除
パイプライン実行ヘルパ

_run_pipeline_and_savefiles(…) → run_pipeline をバックグラウンド実行 → RIS ファイルを書き出し（一時ディレクトリ） → HTML/JSON 返却用にパスを整形
進捗付きパイプライン

run_pipeline_with_progress(job_id,…) → Janus あるいは asyncio.Queue でコールバック進捗を蓄積 → 完了後 jobs[job_id]["result"] にまとめ
HTTP エンドポイント群
・GET / → /index.html へリダイレクト
・GET /api/run_stream_get
└ 同期実行：JSON で最終結果だけ返す
・POST /api/run_stream
└ フォーム or ファイルアップロードで同上
・GET /api/run_stream_sse
└ http-stream（SSE）でステップごとの進捗＋完了時データ
・POST /api/run_stream_start + GET /api/run_stream_events
└ ジョブ方式：
1. start でジョブID発行＋バックグラウンド実行開始
2. events で jobs[job_id] をポーリングし SSE 送信
・GET /download/{fname} → 一時ディレクトリ上の RIS/JSON を返却
・GET /health → 状態チェック
・StaticFiles → /static 配下を HTML/JS/CSS として公開

――――――――――――――――――――――
3. pipeline.py の役割
――――――――――――――――――――――

初期化

Entrez.email 設定（NCBI API）
OpenAI クライアント初期化（API キー検証）
LLM モデル（gpt-4.1-mini, gpt-4.1 etc.）
ユーティリティ関数

split_sentences：句読点で分割、小数点や省略形を保護
push(progress, obj)：SSE／ジョブ進捗コールバック
endnote_tag, to_ris：EndNote タグ／RIS フォーマット生成
PubMed／PMC 取得

get_pmcid_from_pmid, get_pmc_fulltext：PMC XML から本文取得
get_paper_details：Entrez.efetch → MEDLINE テキスト解析 → 辞書化
GPT 呼び出しヘルパー

extract_keywords：文章からバイオ医系キーワード（5個まで）抽出
build_queries：キーワード２〜３組み合わせで PubMed 検索式生成
ai_is_citable_scored：文献要旨＋本文断片を LLM へ送り「5点満点評価＋理由」
ai_parallel：ThreadPoolExecutor で並列スコアリング＋進捗報告
文献検索ステップ

find_citable_references：esearch で PMIDs → get_paper_details → ai_parallel
上位絞り込み

select_refs_with_llm：スコア>=4 の候補から、LLM に「上位 N 本選べ」の指示 → 出力整形（番号・PMID 抽出）→ 上位 N 本返却＋説明テキスト
SubordinateAgent（文単位エージェント）

analyze_sentence(sent, idx)： ① キーワード→クエリ生成 ② 各クエリで文献検索＋詳細＋スコアリング ③ スコア>=4 のプールから選定 ④ 詳細行（phase/query/rank/select/...）を row_cb へ蓄積 ⑤ 選定済みリストを返す
EditAgent（編集エージェント）

insert_and_export_endnote： ・文末に EndNote タグ挿入 ・引用文献をまとめて RIS ブロック化 ・（必要なら .txt/.ris ファイル出力）
レポート書き出し

write_report：簡易レポート（sent_idx,pmid,score,reason,accepted）を CSV/JSON
write_detail_report：DETAIL_HEADERS に沿った詳細レポートを CSV/JSON
run_pipeline（パイプライン本体）

文分割（split_sentences）
ThreadPoolExecutor(max_workers=2) で各文を SubordinateAgent に投入
返ってきた参照リストを refs_all に格納
row_cb で集めた detail_rows を取り出す
EditAgent で引用付きテキスト＋RIS を生成
write_report / write_detail_report でレポート用ファイルを生成
最終結果 (cited, ris, report_base, detail_base) を返す
――――――――――――――――――――――
4. 全体データフローまとめ
――――――――――――――――――――――

ユーザーリクエスト
↓
FastAPI エンドポイント
↓
（SSE or ジョブ or 同期）で run_pipeline 呼び出し
↓
pipeline.py:
文分割 → キーワード→PubMed検索→LLMスコア→選定→引用挿入＋RIS／レポート
↓
一時ファイル書き出し＋結果組立
↓
FastAPI 返却（JSON or SSE）
↓
フロントエンド描画

――――――――――――――――――――――

以上が両ファイルの仕様・内部動作の全体像です。何か不明点あれば補足します！