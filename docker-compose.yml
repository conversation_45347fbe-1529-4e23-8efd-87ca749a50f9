services:
  postgres:
    image: postgres:13
    restart: unless-stopped
    env_file: [.env]
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $${POSTGRES_DB}"]
      interval: 5s
      timeout: 3s
      retries: 10

  citer2:
    image: atmiyashita/citer2:1     # ← 固定タグ
    restart: unless-stopped
    env_file: [.env]
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./alembic/versions:/app/alembic/versions
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:${POSTGRES_PORT}/${POSTGRES_DB}
      - GOOGLE_CREDENTIALS_BASE64=${GOOGLE_CREDENTIALS_BASE64}

  caddy:
    image: caddy:alpine
    restart: unless-stopped
    ports: ["80:80", "443:443"]
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
      - caddy_config:/config
    depends_on: [citer2]

volumes:
  pgdata:
  caddy_data:
  caddy_config: