#!/usr/bin/env python3
"""
Google Cloud Vision API設定テスト
"""

import os
import sys
from pathlib import Path

def test_environment():
    """環境設定のテスト"""
    print("🔍 環境設定をチェック中...")
    
    # 1. 環境変数の確認
    creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    if not creds_path:
        print("❌ GOOGLE_APPLICATION_CREDENTIALS環境変数が設定されていません")
        print("   設定方法:")
        print("   export GOOGLE_APPLICATION_CREDENTIALS='/path/to/your/credentials.json'")
        return False
    
    print(f"✅ 環境変数設定: {creds_path}")
    
    # 2. ファイルの存在確認
    if not os.path.exists(creds_path):
        print(f"❌ 認証ファイルが見つかりません: {creds_path}")
        return False
    
    print(f"✅ 認証ファイル存在確認: OK")
    
    # 3. ファイル権限の確認
    file_stat = os.stat(creds_path)
    file_mode = oct(file_stat.st_mode)[-3:]
    print(f"📁 ファイル権限: {file_mode}")
    
    if file_mode != '600':
        print("⚠️  セキュリティ警告: ファイル権限を600に設定することを推奨")
        print(f"   chmod 600 {creds_path}")
    
    return True

def test_google_vision():
    """Google Vision APIのテスト"""
    print("\n🔍 Google Vision APIをテスト中...")
    
    try:
        from google.cloud import vision
        print("✅ google-cloud-visionパッケージ: インポート成功")
    except ImportError:
        print("❌ google-cloud-visionパッケージがインストールされていません")
        print("   インストール方法:")
        print("   pip install google-cloud-vision")
        return False
    
    try:
        # クライアントの初期化
        client = vision.ImageAnnotatorClient()
        print("✅ Vision APIクライアント: 初期化成功")
        
        # プロジェクトIDの確認
        
        return True
        
    except Exception as e:
        print(f"❌ Vision APIクライアント初期化エラー: {e}")
        print("\n🔧 トラブルシューティング:")
        print("1. Google Cloud ConsoleでVision APIが有効になっているか確認")
        print("2. サービスアカウントに適切なロールが付与されているか確認")
        print("3. 認証ファイルの内容が正しいか確認")
        return False

def test_ocr_service():
    """OCRサービスのテスト"""
    print("\n🔍 OCRサービスをテスト中...")
    
    try:
        # OCRサービスのインポート
        sys.path.append('agent')
        from ocr_service import ocr_service
        print("✅ OCRサービス: インポート成功")
        
        # 利用可能な方法の確認
        available_methods = ocr_service.get_available_methods()
        print(f"📋 利用可能なOCR方法: {available_methods}")
        
        if "google_vision" in available_methods:
            print("✅ Google Vision OCR: 利用可能")
        else:
            print("❌ Google Vision OCR: 利用不可")
            
        if "openai_vision" in available_methods:
            print("✅ OpenAI Vision OCR: 利用可能")
        else:
            print("❌ OpenAI Vision OCR: 利用不可")
            
        return len(available_methods) > 0
        
    except Exception as e:
        print(f"❌ OCRサービステストエラー: {e}")
        return False

def create_sample_image():
    """テスト用のサンプル画像を作成"""
    print("\n🖼️  テスト用画像を作成中...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        import io
        
        # 白い背景の画像を作成
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # テキストを描画
        text = "実験ノート\n日付: 2024/06/19\n結果: 良好"
        
        try:
            # フォントを試行
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        draw.text((50, 50), text, fill='black', font=font)
        
        # バイトデータに変換
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        
        print("✅ テスト画像作成: 成功")
        return img_bytes.getvalue()
        
    except ImportError:
        print("❌ PIL(Pillow)がインストールされていません")
        print("   インストール方法: pip install Pillow")
        return None
    except Exception as e:
        print(f"❌ テスト画像作成エラー: {e}")
        return None

def test_ocr_functionality():
    """実際のOCR機能をテスト"""
    print("\n🔍 OCR機能をテスト中...")
    
    # テスト画像を作成
    test_image = create_sample_image()
    if not test_image:
        print("❌ テスト画像の作成に失敗")
        return False
    
    try:
        sys.path.append('agent')
        from ocr_service import ocr_service
        
        # OCR実行
        result = ocr_service.extract_text(test_image, method="auto")
        
        if result["success"]:
            print("✅ OCR処理: 成功")
            print(f"📋 使用方法: {result['method']}")
            print(f"📋 信頼度: {result['confidence']:.2f}")
            print(f"📋 抽出テキスト: {result['text'][:100]}...")
            return True
        else:
            print(f"❌ OCR処理失敗: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ OCR機能テストエラー: {e}")
        return False

def main():
    """メインテスト関数"""
    print("🚀 Google Cloud Vision API設定テストを開始")
    print("=" * 50)
    
    # 環境設定テスト
    if not test_environment():
        print("\n❌ 環境設定に問題があります")
        return False
    
    # Google Vision APIテスト
    if not test_google_vision():
        print("\n❌ Google Vision API設定に問題があります")
        return False
    
    # OCRサービステスト
    if not test_ocr_service():
        print("\n❌ OCRサービスに問題があります")
        return False
    
    # OCR機能テスト
    if not test_ocr_functionality():
        print("\n❌ OCR機能に問題があります")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 すべてのテストが成功しました！")
    print("Google Cloud Vision APIが正しく設定されています。")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
