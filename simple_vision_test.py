#!/usr/bin/env python3
"""
シンプルなGoogle Vision APIテスト
"""

import os
import json

def test_simple():
    print("🔍 シンプルなGoogle Vision APIテスト")
    
    # 1. 環境変数確認
    creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    print(f"認証ファイルパス: {creds_path}")
    
    if not creds_path:
        print("❌ 環境変数が設定されていません")
        return False
    
    # 2. 絶対パスに変換
    abs_creds_path = os.path.abspath(creds_path)
    print(f"絶対パス: {abs_creds_path}")
    
    # 3. ファイル存在確認
    if not os.path.exists(abs_creds_path):
        print(f"❌ ファイルが存在しません: {abs_creds_path}")
        return False
    
    # 4. 環境変数を絶対パスに更新
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = abs_creds_path
    
    # 5. JSONファイル読み込み
    try:
        with open(abs_creds_path, 'r') as f:
            creds = json.load(f)
        print(f"✅ JSON読み込み成功")
        print(f"プロジェクトID: {creds.get('project_id')}")
        print(f"サービスアカウント: {creds.get('client_email')}")
    except Exception as e:
        print(f"❌ JSON読み込みエラー: {e}")
        return False
    
    # 6. Google Cloud Vision API テスト
    try:
        from google.cloud import vision
        print("✅ google-cloud-visionインポート成功")
        
        # クライアント初期化
        client = vision.ImageAnnotatorClient()
        print("✅ Vision APIクライアント初期化成功")
        
        # プロジェクト情報取得
        project_id = creds.get('project_id')
        print(f"✅ 使用プロジェクト: {project_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Vision API初期化エラー: {e}")
        print(f"エラータイプ: {type(e).__name__}")
        
        # 詳細なエラー情報
        if "DefaultCredentialsError" in str(type(e)):
            print("🔧 認証情報の問題です")
            print("1. 環境変数が正しく設定されているか確認")
            print("2. JSONファイルの内容が正しいか確認")
            print("3. サービスアカウントに適切な権限があるか確認")
        
        return False

if __name__ == "__main__":
    success = test_simple()
    if success:
        print("\n🎉 Google Vision API設定成功！")
    else:
        print("\n❌ 設定に問題があります")
