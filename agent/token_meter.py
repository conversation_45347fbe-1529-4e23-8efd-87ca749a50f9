# token_meter.py
class TokenMeter:
    PRICE = {
        ("gpt-4.1",      "prompt"): 0.01,
        ("gpt-4.1",      "completion"): 0.03,
        ("gpt-4.1-mini", "prompt"): 0.003,
        ("gpt-4.1-mini", "completion"): 0.006,
        ("o4-mini", "prompt"): 0.005,
        ("o4-mini", "completion"): 0.020
    }
    def __init__(self): self.tot = {k:0 for k in self.PRICE}
    def add(self, model, prom, comp):
        self.tot[(model,"prompt")]     += prom
        self.tot[(model,"completion")] += comp
    def usd(self):
        return sum(t/1000*self.PRICE[k] for k,t in self.tot.items())
METER = TokenMeter()