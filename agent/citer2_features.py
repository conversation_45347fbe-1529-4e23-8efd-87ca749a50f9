"""
Citer2専用機能
- シンプルな文献引用
- 基本的な重複除去
- 全文対象の検索
"""

from typing import List, Dict, Any, Optional


def simple_citation_cleanup(text: str, model: str = "gpt-4.1-mini") -> str:
    """
    Citer2用のシンプルなテキスト整理
    重複引用番号の除去のみ実行
    """
    from agent.pipeline import clean_duplicate_citations
    return clean_duplicate_citations(text, model)


def identify_all_sentences(text: str, sentences: List[str]) -> List[int]:
    """
    Citer2用：全ての文を対象にする
    """
    return list(range(len(sentences)))


def citer2_enhanced_pipeline(sent, query, hits, progress=None):
    """
    Citer2用のシンプルな引用パイプライン
    高度な分類機能は使用せず、基本的な文献検索のみ
    """
    from agent.pipeline import find_citable_references
    return find_citable_references(sent, query, hits, progress)


def citer2_sentence_processing_config():
    """
    Citer2用の文処理設定
    """
    return {
        "enable_advanced_features": False,
        "enable_section_filtering": False,
        "enable_figure_detection": False,
        "enable_sentence_classification": False,
        "default_threshold": 3,  # より多くの文献を含める
        "max_workers": 4
    }


def format_citer2_results(cited_text: str, references: List[Dict]) -> Dict[str, Any]:
    """
    Citer2用の結果フォーマット
    シンプルな構造で返す
    """
    return {
        "cited_text": cited_text,
        "references": references,
        "reference_count": len(references),
        "format": "citer2_simple"
    }


def validate_citer2_input(text: str) -> Dict[str, Any]:
    """
    Citer2用の入力検証
    """
    if not text or not text.strip():
        return {
            "valid": False,
            "error": "テキストが空です"
        }
    
    # 文の数をチェック
    from agent.pipeline import split_sentences
    sentences = split_sentences(text)
    
    if len(sentences) == 0:
        return {
            "valid": False,
            "error": "有効な文が見つかりません"
        }
    
    # 長すぎるテキストのチェック（Citer2は短いテキスト向け）
    if len(text) > 10000:  # 10KB制限
        return {
            "valid": False,
            "error": "テキストが長すぎます（10,000文字以下にしてください）"
        }
    
    return {
        "valid": True,
        "sentence_count": len(sentences),
        "character_count": len(text)
    }


def get_citer2_search_queries(sentence: str) -> List[str]:
    """
    Citer2用の検索クエリ生成
    シンプルなキーワード抽出
    """
    import re
    
    # 基本的なキーワード抽出
    # 英語の場合
    if re.search(r'[a-zA-Z]', sentence):
        # 英語キーワードを抽出
        words = re.findall(r'\b[a-zA-Z]{3,}\b', sentence)
        # 一般的すぎる単語を除外
        stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
        keywords = [w for w in words if w.lower() not in stop_words]
        
        if len(keywords) >= 2:
            return [f'"{keywords[0]}"[Title/Abstract] AND "{keywords[1]}"[Title/Abstract]']
        elif len(keywords) == 1:
            return [f'"{keywords[0]}"[Title/Abstract]']
    
    # 日本語の場合は基本的なクエリを返す
    return ["infection models", "drug discovery"]


def citer2_progress_callback(step: str, message: str, progress_data: Optional[Dict] = None):
    """
    Citer2用の進捗コールバック
    シンプルなログ出力
    """
    print(f"CITER2 [{step}]: {message}")
    if progress_data:
        print(f"CITER2 Progress: {progress_data}")


def estimate_citer2_processing_time(text: str) -> int:
    """
    Citer2の処理時間を推定（秒）
    """
    from agent.pipeline import split_sentences
    sentences = split_sentences(text)
    
    # 1文あたり約3-5秒と推定
    base_time = len(sentences) * 4
    
    # 最小5秒、最大60秒
    return max(5, min(60, base_time))


def citer2_error_handler(error: Exception, context: str) -> Dict[str, Any]:
    """
    Citer2用のエラーハンドリング
    """
    error_message = str(error)
    
    # よくあるエラーの分類
    if "timeout" in error_message.lower():
        return {
            "error_type": "timeout",
            "message": "処理がタイムアウトしました。テキストを短くして再試行してください。",
            "suggestion": "文章を分割して個別に処理することをお勧めします。"
        }
    elif "api" in error_message.lower():
        return {
            "error_type": "api_error",
            "message": "外部APIでエラーが発生しました。しばらく待ってから再試行してください。",
            "suggestion": "問題が続く場合は、サポートにお問い合わせください。"
        }
    elif "network" in error_message.lower():
        return {
            "error_type": "network_error",
            "message": "ネットワークエラーが発生しました。インターネット接続を確認してください。",
            "suggestion": "接続が安定してから再試行してください。"
        }
    else:
        return {
            "error_type": "unknown",
            "message": f"予期しないエラーが発生しました: {error_message}",
            "suggestion": "問題が続く場合は、サポートにお問い合わせください。"
        }


def citer2_post_process(cited_text: str, references: List[Dict]) -> Dict[str, Any]:
    """
    Citer2用の後処理
    """
    # シンプルな重複除去のみ
    processed_text = simple_citation_cleanup(cited_text)
    
    # 参照リストの基本的な検証
    valid_references = []
    for ref in references:
        if ref.get("pmid") and ref.get("title"):
            valid_references.append(ref)
    
    return {
        "cited_text": processed_text,
        "references": valid_references,
        "processing_notes": {
            "original_references": len(references),
            "valid_references": len(valid_references),
            "text_length": len(processed_text)
        }
    }
