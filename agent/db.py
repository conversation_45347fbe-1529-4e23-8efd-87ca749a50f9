# agent/db.py
import os
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import (
    AsyncSession, create_async_engine
)
from sqlalchemy.orm import sessionmaker, DeclarativeBase

# ───────────────────────────────────────────────
# 1) エンジン作成
# ───────────────────────────────────────────────
DATABASE_URL = os.getenv(
    "DATABASE_URL"
    #,"sqlite+aiosqlite:///./db.sqlite3"        # ← デフォルトは SQLite
)

engine = create_async_engine(
    DATABASE_URL,
    echo=False,        # True にすると SQL がログに出る
    future=True,
)

# ───────────────────────────────────────────────
# 2) sessionmaker (ここが SessionLocal)
# ───────────────────────────────────────────────
SessionLocal = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    autoflush=True,
    expire_on_commit=False,
)

# ───────────────────────────────────────────────
# 3) FastAPI 用 Depends ラッパ
#    (local_kw 等を公開しない)
# ───────────────────────────────────────────────
async def get_session_local():
    """
    FastAPI の Depends で使う非同期セッションファクトリ。
    SessionLocal が内部で持つ **local_kw 可変引数** を
    エンドポイントに露出させないためのラッパ。
    """
    async with SessionLocal() as session:
        yield session

# ───────────────────────────────────────────────
# 4) 互換：従来の asynccontextmanager 版
#    （他ファイルで Depends(get_session) を使っている場合のため）
# ───────────────────────────────────────────────
@asynccontextmanager
async def get_session():
    async with SessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# ───────────────────────────────────────────────
# 5) Base クラス（モデル定義で継承する）
# ───────────────────────────────────────────────
class Base(DeclarativeBase):
    """SQLAlchemy Declarative Base"""
    pass