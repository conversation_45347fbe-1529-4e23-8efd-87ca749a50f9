# agent/schemas.py

import uuid
from typing import Optional, List
from fastapi_users import schemas
from pydantic import BaseModel, EmailStr, Field, ConfigDict
import datetime

# ── FastAPI-Users 用 ───────────────────────────────────────
class UserCreate(schemas.BaseUserCreate):
    role: int = 0
    stripe_customer_id: Optional[str] = None

class UserUpdate(schemas.BaseUserUpdate):
    role: Optional[int] = None
    stripe_customer_id: Optional[str] = None

class UserDB(schemas.BaseUser[uuid.UUID]):
    role: int
    stripe_customer_id: Optional[str]

    class Config:
        from_attributes = True


class UserMe(BaseModel):
    email:               str
    role:                int
    subscription_end:    Optional[str]
    subscription_status: Optional[str]
    gmail_connected:     bool
    calendar_connected:  bool
    gmail_address:       Optional[str]           # 追加
    calendar_address:    Optional[str]           # 追加
    last_gmail_sync:     Optional[datetime.datetime]
    last_calendar_sync:  Optional[datetime.datetime]

    class Config:
        from_attributes = True

# ── ToDo / Calendar スキーマ ─────────────────────────────
class TodoItem(BaseModel):
    # Pydantic v2 向け設定
    model_config = ConfigDict(from_attributes=True, populate_by_name=True)
    
    # ORM: task_id → alias で id
    id:   uuid.UUID  = Field(..., alias="task_id")
    # ORM: task_text → alias で task
    task: str  = Field(..., alias="task_text")

    due:      Optional[str] = None
    priority: int
    mail_id:  str
    link:     str
    category: Optional[str] = None
    hidden:   bool          = False
    
class TodoItemUpdate(BaseModel):
    """
    PATCH /api/todos/{task_id} で受け取る更新用スキーマ。
    今回はカテゴリのみ更新可能とする仕様にしておきます。（宮下）
    """
    category: Optional[str] = None


class EventItem(BaseModel):
    id:      str
    summary: str
    start:   str
    end:     str

class ScheduleItem(BaseModel):
    # task_id/start/end を Optional[str] にして None あるいは欠落を許容
    task_id: Optional[str] = None
    start:   Optional[str] = None
    end:     Optional[str] = None
    note:    Optional[str] = None

class TodosResponse(BaseModel):
    todos:    List[TodoItem]
    events:   List[EventItem]
    schedule: List[ScheduleItem]
    
# ── タスク分類ようのカテゴリスキーマ ────────────────────────────────────   
class CategoryItem(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    category_id: int
    name:        str
    order:       Optional[int]
    

class CategoryCreate(BaseModel):
    name:  str
    order: Optional[int] = None

class CategoryUpdate(BaseModel):
    name:  Optional[str]
    order: Optional[int]
    
# ── トピック関与者抽出レスポンス ─────────────────────────────
class ParticipantsResponse(BaseModel):
    topic:        str
    participants: List[str]