# agent/auth.py

import os
import uuid
import datetime as dt
import jwt as jwt_lib  # PyJWT
import logging
from typing import Optional, Union, Dict

from fastapi import Depends, APIRouter, Request, HTTPException, Query, Response
from fastapi.responses import RedirectResponse, JSONResponse
from authlib.integrations.starlette_client import OAuth
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from fastapi_users import FastAPIUsers, exceptions as fau_exc
from fastapi_users.db import SQLAlchemyUserDatabase
from fastapi_users.manager import BaseUserManager
from fastapi_users.authentication import AuthenticationBackend, BearerTransport, JWTStrategy
from agent.schemas import User<PERSON><PERSON>, UserDB, UserMe
from agent.models import User as ORMUser, Subscription
from agent.db import get_session_local
from agent.gmail_todos_helper import SCOPES_GMAIL, SCOPES_CALENDAR

from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.exceptions import RefreshError
from googleapiclient.errors import HttpError 


logger = logging.getLogger(__name__)

# ──────────────────────────────────────────────────────────
# 1. 環境変数
# ──────────────────────────────────────────────────────────
SECRET               = os.getenv("SECRET_KEY")
GOOGLE_CLIENT_ID     = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
GOOGLE_REDIRECT_URI  = os.getenv("GOOGLE_REDIRECT_URI")
for name, val in {
    "SECRET_KEY": SECRET,
    "GOOGLE_CLIENT_ID": GOOGLE_CLIENT_ID,
    "GOOGLE_CLIENT_SECRET": GOOGLE_CLIENT_SECRET,
    "GOOGLE_REDIRECT_URI": GOOGLE_REDIRECT_URI,
}.items():
    if not val:
        raise RuntimeError(f"{name} env var is required")

# ──────────────────────────── 2. OAuth スコープ
# Gmail / Calendar どちらでも baseScopes + サービス別スコープを要求
BASE_SCOPES = ["openid", "email", "profile"]
GMAIL_SCOPES    = BASE_SCOPES + ["https://www.googleapis.com/auth/gmail.readonly"]
# 🔧 CalendarList を読むには calendar.readonly が必要
CALENDAR_SCOPES = BASE_SCOPES + ["https://www.googleapis.com/auth/calendar.readonly",
                                 "https://www.googleapis.com/auth/calendar.events.readonly",]

# ──────────────────────────────────────────────────────────
# 3. UserManager & DB (fastapi-users)
# ──────────────────────────────────────────────────────────
async def get_user_db(session: AsyncSession = Depends(get_session_local)):
    yield SQLAlchemyUserDatabase(session, ORMUser)

class UserManager(BaseUserManager[ORMUser, uuid.UUID]):
    reset_password_token_secret = SECRET
    verification_token_secret   = SECRET

    def parse_id(self, value: str) -> uuid.UUID:
        return uuid.UUID(value)

async def get_user_manager(user_db=Depends(get_user_db)):
    yield UserManager(user_db)

# ──────────────────────────────────────────────────────────
# 4. JWT 認証バックエンド
# ──────────────────────────────────────────────────────────
bearer_transport = BearerTransport(tokenUrl="auth/jwt/login")

def get_jwt_strategy() -> JWTStrategy:
    # 1日（24時間）の有効期限に延長
    return JWTStrategy(secret=SECRET, lifetime_seconds=24*60*60)

jwt_backend = AuthenticationBackend(
    name="jwt",
    transport=bearer_transport,
    get_strategy=get_jwt_strategy,
)

fastapi_users       = FastAPIUsers[ORMUser, uuid.UUID](get_user_manager, [jwt_backend])
current_user        = fastapi_users.current_user()
current_active_user = fastapi_users.current_user(active=True)


def role_required(min_role: int):
    """
    user.role < min_role の場合、JSONResponse({'plan_required':True, 'upgrade_url':…})
    を status_code=200 で返して処理を打ち切る Depends。
    """
    async def dependency(user: UserDB = Depends(current_active_user)):
        if user.role < min_role:
            return JSONResponse(
                status_code=200,
                content={
                    "plan_required": True,
                    "upgrade_url": "/billing.html",
                    "message": "Your plan does not allow this operation. Please upgrade."
                }
            )
        return user
    return Depends(dependency)


# ──────────────────────────────────────────────────────────
# 5. Authlib 設定
# ──────────────────────────────────────────────────────────
oauth = OAuth()
oauth.register(
    name="google",
    client_id=GOOGLE_CLIENT_ID,
    client_secret=GOOGLE_CLIENT_SECRET,
    server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
    client_kwargs={},   # scope は authorize_redirect で与える
)


google_router = APIRouter(prefix="/auth/google", tags=["auth"])

# --- ① 互換ルート: /auth/google/login ---------------
#   以前のフロントコードが叩くパスに合わせたエイリアス。
@google_router.get("/login", include_in_schema=False)
async def _login_alias(request: Request):
    return await google_sso_login(request)


# --- ② SSO 用ログイン -------------------------------
@google_router.get("/sso/login")
async def google_sso_login(request: Request):
    request.session["oauth_service"] = "sso"
    return await oauth.google.authorize_redirect(
        request,
        GOOGLE_REDIRECT_URI,
        prompt="select_account",
        scope="openid email profile",
        access_type="offline",          # 🔧 refresh_token を確実に取得
        include_granted_scopes="true",
    )


# --- ③ Gmail / Calendar 連携開始 --------------------
@google_router.get("/connect/{service}")
async def google_connect(
    request: Request,
    service: str,
    jwt_token: str = Query(..., alias="jwt"),
):
    if service not in ("gmail", "calendar"):
        raise HTTPException(400, "service must be 'gmail' or 'calendar'")

    # JWT 検証
    try:
        payload = jwt_lib.decode(jwt_token, SECRET, audience=["fastapi-users:auth"], algorithms=["HS256"])
        user_id = payload["sub"]
    except Exception:
        raise HTTPException(401, "Invalid JWT")

    request.session.update({"user_id": user_id, "oauth_service": service})
    scopes = GMAIL_SCOPES if service == "gmail" else CALENDAR_SCOPES
    return await oauth.google.authorize_redirect(
        request,
        GOOGLE_REDIRECT_URI,
        prompt="consent",              # 🔧 毎回 refresh_token をもらう
        access_type="offline",
        scope=" ".join(scopes),
        include_granted_scopes="true",
    )

# --- ④ Gmail / Calendar 切断 ------------------------
@google_router.post("/disconnect/{service}")
async def google_disconnect(
    service: str,
    user: ORMUser = Depends(current_active_user),
    session: AsyncSession = Depends(get_session_local),
):
    if service == "gmail":
        user.gmail_tokens = None
    elif service == "calendar":
        user.calendar_tokens = None
    else:
        raise HTTPException(400, "invalid service")
    await session.commit()
    return {"status": "disconnected", "service": service}


# --- ⑤ OAuth2 コールバック --------------------------
@google_router.get("/callback")
async def google_callback(
    request: Request,
    session: AsyncSession = Depends(get_session_local),
    user_manager: UserManager = Depends(get_user_manager),
):
    svc     = request.session.pop("oauth_service", None)
    user_id = request.session.pop("user_id", None)

    try:
        # ── 1. アクセストークン取得 ─────────────────────────
        token = await oauth.google.fetch_access_token(
            redirect_uri=GOOGLE_REDIRECT_URI,
            code=request.query_params["code"],
        )

        # ── 2. refresh_token の補完（Gmail/Calendar 連携のみ） ─
        if svc in ("gmail", "calendar") and "refresh_token" not in token:
            old_tokens = None
            if svc == "gmail":
                old_tokens = (await session.get(ORMUser, uuid.UUID(user_id))).gmail_tokens
            else:
                old_tokens = (await session.get(ORMUser, uuid.UUID(user_id))).calendar_tokens

            if old_tokens and old_tokens.get("refresh_token"):
                token["refresh_token"] = old_tokens["refresh_token"]
            else:
                raise HTTPException(
                    400,
                    "Google が refresh_token を返しませんでした。"
                    "Google アカウント → セキュリティ → サードパーティ アクセス で "
                    "本アプリを削除してから再度 Connect してください。"
                )

        # ── 3. ユーザー情報取得 ────────────────────────────
        userinfo = (await oauth.google.get(
            "https://openidconnect.googleapis.com/v1/userinfo",
            token=token,
        )).json()
        email = userinfo["email"]

        # ── 4. ユーザー取得 or 作成 ────────────────────────
        if svc == "sso":
            try:
                user = await user_manager.get_by_email(email)
            except fau_exc.UserNotExists:
                dummy_pass = jwt_lib.encode(
                    {"sub": userinfo["sub"],
                     "exp": dt.datetime.utcnow() + dt.timedelta(days=365)},
                    SECRET, algorithm="HS256")
                user = await user_manager.create(UserCreate(
                    email=email,
                    password=dummy_pass,
                    is_verified=True,
                    role=0,
                ))
        else:
            if not user_id:
                raise HTTPException(400, "Missing user id in session")
            user = await session.get(ORMUser, uuid.UUID(user_id))
            if not user:
                raise HTTPException(404, "User not found")

        # ── 5. トークン保存（refresh_token は merge_tokens で保証済み） ─
        def merge_tokens(old: Optional[dict], new: dict) -> dict | None:
            if new.get("refresh_token"):
                return new
            if old and old.get("refresh_token"):
                new["refresh_token"] = old["refresh_token"]
                return new
            return None

        creds_common = {
            "token":         token["access_token"],
            "token_uri":     "https://oauth2.googleapis.com/token",
            "client_id":     GOOGLE_CLIENT_ID,
            "client_secret": GOOGLE_CLIENT_SECRET,
        }

        if svc == "gmail":
            merged = merge_tokens(user.gmail_tokens, token)
            if not merged:
                raise HTTPException(400, "refresh_token が取得できませんでした。再度 Connect してください。")
            user.gmail_tokens = {**creds_common, "refresh_token": merged["refresh_token"]}

        elif svc == "calendar":
            merged = merge_tokens(user.calendar_tokens, token)
            if not merged:
                raise HTTPException(400, "refresh_token が取得できませんでした。再度 Connect してください。")
            user.calendar_tokens = {**creds_common, "refresh_token": merged["refresh_token"]}

        await session.commit()

        # ── 6. JWT を発行してリダイレクト ─────────────────
        new_jwt = await jwt_backend.get_strategy().write_token(user)
        dest    = "/todos.html" if svc in ("gmail", "calendar") else "/"
        return RedirectResponse(f"{dest}?jwt={new_jwt}")

    except HTTPException:
        # 業務エラーはそのまま返すがロールバックは必ず行う
        await session.rollback()
        raise
    except Exception as e:
        # 想定外エラーは 500
        await session.rollback()
        logger.error("OAuth callback fatal: %s", e, exc_info=True)
        raise HTTPException(500, "OAuth callback failed")
# ──────────────────────────────────────────────────────────
# 6. /auth/* 標準エンドポイント
# ──────────────────────────────────────────────────────────
auth_router = APIRouter(prefix="/auth", tags=["auth"])

@auth_router.get("/me", response_model=UserMe)
async def get_me(
    me: UserDB = Depends(current_active_user),
    session: AsyncSession = Depends(get_session_local),
):
    # ORM 側ユーザー取得
    db_user = await session.get(ORMUser, me.id)

    # サブスクリプション取得
    result = await session.execute(
        select(Subscription)
        .where(Subscription.user_id == me.id)
        .order_by(Subscription.current_period_end.desc())
    )
    s = result.scalars().first()

    # Gmail 接続情報
    gmail_address    = None
    last_gmail_sync  = None
    if db_user.gmail_tokens:
        try:
            creds = Credentials(
                token=           db_user.gmail_tokens["token"],
                refresh_token=   db_user.gmail_tokens.get("refresh_token"),
                token_uri=       db_user.gmail_tokens["token_uri"],
                client_id=       db_user.gmail_tokens["client_id"],
                client_secret=   db_user.gmail_tokens["client_secret"],
                scopes=          SCOPES_GMAIL,
            )
            svc = build("gmail", "v1", credentials=creds, cache_discovery=False)
            profile = svc.users().getProfile(userId="me").execute()
            gmail_address   = profile.get("emailAddress")
            last_gmail_sync = dt.datetime.utcnow()
        except (RefreshError, HttpError) as e:
            logger.warning("Gmail profile fetch failed, skipping: %s", e)

    # Calendar 接続情報
    calendar_address   = None
    last_calendar_sync = None
    if db_user.calendar_tokens:
        try:
            creds = Credentials(
                token=           db_user.calendar_tokens["token"],
                refresh_token=   db_user.calendar_tokens.get("refresh_token"),
                token_uri=       db_user.calendar_tokens["token_uri"],
                client_id=       db_user.calendar_tokens["client_id"],
                client_secret=   db_user.calendar_tokens["client_secret"],
                scopes=          SCOPES_CALENDAR,
            )
            svc = build("calendar", "v3", credentials=creds)
            cal = svc.calendarList().get(calendarId="primary").execute()
            calendar_address   = cal.get("id")
            last_calendar_sync = dt.datetime.utcnow()
        except (RefreshError, HttpError) as e:
            logger.warning("Calendar profile fetch failed, skipping: %s", e)

    return {
        "email":               me.email,
        "role":                me.role,
        "subscription_end":    s.current_period_end.isoformat() if s and s.current_period_end else None,
        "subscription_status": s.status if s else None,
        "gmail_connected":     bool(db_user.gmail_tokens),
        "calendar_connected":  bool(db_user.calendar_tokens),
        "gmail_address":       gmail_address,
        "calendar_address":    calendar_address,
        "last_gmail_sync":     last_gmail_sync,
        "last_calendar_sync":  last_calendar_sync,
    }

@auth_router.post("/logout", status_code=204)
async def logout(_: ORMUser = Depends(current_active_user)):
    return Response(status_code=204)


