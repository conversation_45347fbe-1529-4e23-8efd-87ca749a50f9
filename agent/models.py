# agents/models.py
import datetime as dt
import uuid
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Boolean, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>
from agent.db import Base  # ここはそのままでOK



class User(Base):
    __tablename__ = "user"

    id                 = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email              = Column(String, unique=True, index=True, nullable=False)
    hashed_password    = Column(String, nullable=False)
    is_active          = Column(Boolean, default=True, nullable=False)
    is_superuser       = Column(Boolean, default=False, nullable=False)
    is_verified        = Column(Boolean, default=False, nullable=False)
    created_at         = Column(DateTime, default=dt.datetime.utcnow, nullable=False)
    role               = Column(Integer, default=0)  # 0 guest / 1 free / 2 paid
    gmail_tokens       = Column(JSON, nullable=True)  # OAuth2 token info
    calendar_tokens    = Column(JSON, nullable=True)
    stripe_customer_id = Column(String, unique=True, nullable=True)
    hidden_todos       = relationship("HiddenTodo",back_populates="user", cascade="all, delete-orphan",)

class TodoItemDB(Base):
    __tablename__ = "todo_items"

    # ① task_id：個別タスクを一意に識別する主キー
    #    → 永続化した GPT 返却 ID をそのまま保持、なければ UUID
    task_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # ② メール ID：既存 mail_id をそのまま持つ（FK は不要）
    mail_id = Column(String, nullable=False, index=True)

    # ③ タスク本文
    task_text = Column(String, nullable=False)

    # ④ 正規化＋重複チェック用ハッシュ
    task_text_hash = Column(String(64), nullable=False, index=True)

    # ⑤ カテゴリ等のメタ
    category    = Column(String, index=True, nullable=True)
    due         = Column(String, nullable=True)
    priority    = Column(Integer, default=3, nullable=False)
    link        = Column(String, nullable=True)

    # ⑥ 挿入時刻
    created_at  = Column(DateTime, default=dt.datetime.utcnow, nullable=False)

    # 関連：HiddenTodo
    hidden_todos = relationship(
        "HiddenTodo",
        back_populates="todo_item",
        cascade="all, delete-orphan",
    )

class HiddenTodo(Base):
    __tablename__ = "hidden_todos"
    id        = Column(Integer, primary_key=True, index=True)
    user_id   = Column(UUID(as_uuid=True), ForeignKey("user.id", ondelete="CASCADE"), nullable=False, index=True)
    mail_id   = Column(String, index=True)             # Gmail の messageId をキーに
    # 追加：task_id 単位での非表示制御
    task_id    = Column(
                   UUID(as_uuid=True),
                   ForeignKey("todo_items.task_id", ondelete="CASCADE"),
                   nullable=True,    # 既存データのため一時的に NULL を許容
                   index=True,
                )
    created_at= Column(DateTime, default=dt.datetime.utcnow)
    user = relationship("User", back_populates="hidden_todos")
    todo_item = relationship("TodoItemDB", back_populates="hidden_todos")

class Credit(Base):
    __tablename__ = "credits"
    user_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), primary_key=True)
    balance = Column(Integer, default=0)
    updated = Column(DateTime, default=dt.datetime.utcnow, onupdate=dt.datetime.utcnow)

class Payment(Base):
    __tablename__ = "payments"
    id          = Column(String, primary_key=True)  # Stripe Session ID
    user_id     = Column(UUID(as_uuid=True), ForeignKey("user.id"))
    amount      = Column(Integer)
    status      = Column(String, default="open")  # open / paid / failed
    created_at  = Column(DateTime, default=dt.datetime.utcnow)
    
class Subscription(Base):
    __tablename__ = "subscription"
    id                  = Column(String, primary_key=True)  # Stripe sub id
    user_id             = Column(UUID(as_uuid=True), ForeignKey("user.id"))
    status              = Column(String)                    # active / past_due / canceled
    current_period_end  = Column(DateTime)


class CategoryDB(Base):
    __tablename__ = "categories"
    category_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id     = Column(ForeignKey("user.id"), nullable=False, index=True)
    name        = Column(String(50), nullable=False)
    order       = Column(Integer, default=0)   # 任意：表示順
    __table_args__ = (UniqueConstraint("user_id","name"),)

