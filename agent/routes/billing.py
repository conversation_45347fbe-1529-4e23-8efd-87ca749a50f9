# agent/routes/billing.py
import os, uuid, datetime as dt, logging                              # ★ 追記
import stripe
from fastapi import (
    APIRouter, Depends, HTTPException,
    BackgroundTasks, Request, Header
)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from agent.db   import get_session_local
from agent.models import Credit, User, Subscription
from agent.auth import current_user, current_active_user

logger = logging.getLogger(__name__)                                  # ★ 追記

stripe.api_key   = os.getenv("STRIPE_SECRET_KEY")
PRICE_ID         = os.getenv("STRIPE_PRICE_ID")
WEBHOOK_SECRET   = os.getenv("STRIPE_WEBHOOK_SECRET")

router = APIRouter(prefix="/billing", tags=["billing"])

# ────────────────────────────────────────────────
# ヘルパ：email から User を取得（見つからなければ None）
# ────────────────────────────────────────────────
async def get_user_by_email(session: AsyncSession, email: str):       # ★ 追記
    result = await session.execute(select(User).where(User.email == email))
    return result.scalars().first()

# ========== 1. balance ==========
@router.get("/balance")
async def balance(
    user: User = Depends(current_user),
    session: AsyncSession = Depends(get_session_local),
):
    credit = await session.get(Credit, user.id)
    return {"balance": credit.balance if credit else 0}

# ========== 2. Checkout ==========
@router.post("/checkout")
async def checkout(
    _: BackgroundTasks,
    user: User = Depends(current_user),
):
    if user.role == 2:
        raise HTTPException(400, detail="already_subscribed")
    try:
        session_obj = stripe.checkout.Session.create(
            mode="subscription",
            customer_email=user.email,
            line_items=[{"price": PRICE_ID, "quantity": 1}],
            success_url="https://myaiagents.duckdns.org/?paid=1",
            cancel_url="https://myaiagents.duckdns.org/",
            metadata={"user_id": str(user.id)},
        )
    except stripe.error.StripeError as e:
        raise HTTPException(502, detail=f"stripe_error:{e.user_message}")
    return {"url": session_obj.url}

# ========== 3. Webhook ==========
@router.post("/webhook")
async def webhook(
    request: Request,
    stripe_signature: str = Header(..., alias="Stripe-Signature"),
    session: AsyncSession = Depends(get_session_local),
):
    payload = await request.body()
    try:
        event = stripe.Webhook.construct_event(payload, stripe_signature, WEBHOOK_SECRET)
    except stripe.error.SignatureVerificationError:
        raise HTTPException(400, "invalid_signature")

    # ---------- checkout.session.completed ----------
    if event["type"] == "checkout.session.completed":
        data       = event["data"]["object"]
        sub_id     = data["subscription"]

        # まず　IDの有無を確認
        if not sub_id:
            logger.warning("[webhook] no subscription ID found; skipping")
            return {"status": "ignored"}
        
        # 次に Strupe からサブスクリプションを取得
        try:
            stripe_sub = stripe.Subscription.retrieve(sub_id)
        except stripe.error.StripeError as e:
            logger.error(f"[webhook] Stripe error retrieving subscription {sub_id}: {e.user_message}")
            return {"status": "ignored"}

        # --- 1) user を取得 ---
        user = None

        # 1-A) metadata の user_id
        meta_uid = data.get("metadata", {}).get("user_id")
        if meta_uid:
            try:
                user = await session.get(User, uuid.UUID(meta_uid))
            except (ValueError, TypeError):
                pass

        # 1-B) customer.email で fallback                             # ★ 追記
        if not user and stripe_sub.get("customer"):
            try:
                cust  = stripe.Customer.retrieve(stripe_sub["customer"])
                email = cust.get("email")
                if email:
                    result = await session.execute(
                        select(User).where(User.email == email)
                    )
                    user = result.scalars().first()
            except Exception as e:
                logger.warning("Failed to lookup user by customer: %s", e)

        if not user:
            logger.warning("[webhook] user not found for subscription %s", sub_id)
            return {"status": "ignored"}

        # --- 2) 期間終了日時 ---
        period_end = None
        tpe = stripe_sub.get("current_period_end")
        if tpe:
            period_end = dt.datetime.utcfromtimestamp(int(tpe))

        # --- 3) DB 反映 ---
        user.role = 2

        sub = await session.get(Subscription, sub_id)
        if not sub:
            sub = Subscription(
                id=sub_id,
                user_id=user.id,
                status="active",
                current_period_end=period_end,
            )
        else:
            sub.status = "active"
            sub.current_period_end = period_end

        await session.merge(sub)
        await session.commit()
        return {"status": "ok"}                                       # ★ 追記

    # ---------- 解約 / 失敗 ----------
    elif event["type"] in ("invoice.payment_failed", "customer.subscription.deleted"):
        obj = event["data"]["object"]
        sub_id = obj["id"]
        stripe_sub = stripe.Subscription.retrieve(sub_id)
        if not sub_id:
            return {"status": "ignored"}
        
        # 1) Stripe Subscription を取得
        try:
            stripe_sub = stripe.Subscription.retrieve(sub_id)
        except Exception as e:
            logger.error("Failed to retrieve subscription %s: %s", sub_id, e)
            return {"status": "ignored"}
        
        # 2) 期間終了日時 
        period_end = None
        tpe = stripe_sub.get("current_period_end")
        if tpe:
            period_end = dt.datetime.utcfromtimestamp(int(tpe))

        # 3) DB 反映
        sub = await session.get(Subscription, sub_id)
        if sub:
            user = await session.get(User, sub.user_id)
            if user:
                user.role = 1
            sub.status             = "canceled"
            sub.current_period_end = period_end
            await session.commit()
        return {"status": "ok"}                            # ★ 追記

    # ---------- その他イベント ----------
    return {"status": "ignored"}                               # ★ 追記

# ========== 4. Unsubscribe ==========
@router.post("/unsubscribe")
async def unsubscribe(
    user: User = Depends(current_active_user),
    session: AsyncSession = Depends(get_session_local),
):
    result = await session.execute(
        select(Subscription)
        .where(
            (Subscription.user_id == user.id) &
            (Subscription.status == "active")
        )
    )
    sub = result.scalars().first()
    if not sub:
        raise HTTPException(400, detail="no_active_subscription")
    try:
        stripe.Subscription.modify(sub.id, cancel_at_period_end=True)
        sub.status = "cancel_at_period_end"
        await session.commit()
        return {"status": "ok"}
    except stripe.error.StripeError as e:
        raise HTTPException(502, detail=f"stripe_error:{e.user_message}")
    except Exception as e:
        raise HTTPException(500, detail=f"internal_error:{e}")