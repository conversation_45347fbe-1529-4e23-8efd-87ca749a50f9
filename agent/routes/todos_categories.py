# agent/routes/todos_categories.py

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, update
from agent.db import get_session_local
from agent.models import CategoryDB, User as ORMUser
from agent.schemas import CategoryItem, CategoryCreate, CategoryUpdate, UserDB
from agent.auth import role_required

router = APIRouter(prefix="/api/categories", tags=["categories"])

DEFAULT_CATS = ["実験","会議","教育","管理","その他"]

@router.get("", response_model=List[CategoryItem])
async def list_categories(me_or_resp: UserDB = role_required(1),
                          session: AsyncSession = Depends(get_session_local)):
    if isinstance(me_or_resp, dict) or isinstance(me_or_resp, Response):
        return me_or_resp
    user_id = me_or_resp.id
    rows = await session.execute(
        select(CategoryDB)
        .where(CategoryDB.user_id == user_id)
        .order_by(CategoryDB.order)
    )
    cats = rows.scalars().all()
    return [CategoryItem.from_orm(c) for c in cats]

@router.post("", response_model=CategoryItem, status_code=201)
async def create_category(input: CategoryCreate,
                          me_or_resp: UserDB = role_required(1),
                          session: AsyncSession = Depends(get_session_local)):
    if isinstance(me_or_resp, dict) or isinstance(me_or_resp, Response):
        return me_or_resp
    new = CategoryDB(user_id=me_or_resp.id, **input.dict())
    session.add(new)
    await session.flush()
    await session.commit()
    return CategoryItem.from_orm(new)

@router.patch("/{category_id}", response_model=CategoryItem)
async def update_category(category_id: int, input: CategoryUpdate,
                          me_or_resp: UserDB = role_required(1),
                          session: AsyncSession = Depends(get_session_local)):
    if isinstance(me_or_resp, dict) or isinstance(me_or_resp, Response):
        return me_or_resp
    stmt = update(CategoryDB).where(
        CategoryDB.user_id == me_or_resp.id,
        CategoryDB.category_id == category_id
    ).values(**{k:v for k,v in input.dict().items() if v is not None}).returning(CategoryDB)
    res = await session.execute(stmt)
    updated = res.fetchone()
    if not updated:
        raise HTTPException(404, "Category not found")
    await session.commit()
    return CategoryItem.from_orm(updated[0])

@router.delete("/{category_id}", status_code=204)
async def delete_category(category_id: int,
                          me_or_resp: UserDB = role_required(1),
                          session: AsyncSession = Depends(get_session_local)):
    if isinstance(me_or_resp, dict) or isinstance(me_or_resp, Response):
        return me_or_resp
    await session.execute(
        delete(CategoryDB).where(
            CategoryDB.user_id == me_or_resp.id,
            CategoryDB.category_id == category_id
        )
    )
    await session.commit()
    return
