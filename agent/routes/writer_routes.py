"""
routes/writer_routes.py
FastAPI のエンドポイントを定義
"""
from __future__ import annotations
from fastapi import APIRouter, UploadFile, File, Form, Body, HTTPException, Response, Depends
from typing import List, Dict, Any, Optional, Union
import uuid
import json
import os
import re
import datetime
from openai import OpenAIError
from agent.services.writer_svc import (
    extract_note_from_bytes,
    compose_paper,
    rewrite_paper,
    add_citations_to_paper
)

from agent.auth import current_active_user, role_required
from agent.db import get_session_local
from sqlalchemy.ext.asyncio import AsyncSession
from agent.models import User as UserDB


router = APIRouter(prefix="/writer", tags=["writer"])

# メモリ内ストレージ（本番環境ではDBに置き換え）
NOTE_STORE = {}   # note_id -> [note_dict, note_dict, ...]
PAPER_STORE = {}  # paper_id -> {"paper": paper_text, "note_dict": note_dict}
USAGE_STORE = {}  # user_id -> {"date": "2023-01-01", "count": 1}

# 使用回数をチェックする関数
def check_usage_limit(user_id: str, limit: int = 3):
    today = datetime.date.today().isoformat()
    
    if user_id not in USAGE_STORE:
        USAGE_STORE[user_id] = {"date": today, "count": 1}
        return True
    
    user_usage = USAGE_STORE[user_id]
    
    # 日付が変わっていれば、カウントをリセット
    if user_usage["date"] != today:
        USAGE_STORE[user_id] = {"date": today, "count": 1}
        return True
    
    # 使用回数が制限以内かチェック
    if user_usage["count"] >= limit:
        return False
    
    # 使用回数をインクリメント
    USAGE_STORE[user_id]["count"] += 1
    return True

# ---------- ① 画像 → 転写 ---------- #
@router.post("/note")
async def upload_note(
    files: List[UploadFile] = File(...),
    me: UserDB = role_required(1)  # Depends() を削除
):
    # 有料ユーザー(role=2)でない場合は使用回数をチェック
    user_id = str(me.id)
    if me.role < 2 and not check_usage_limit(user_id):
        return {
            "limit_exceeded": True,
            "message": "1日の使用回数制限（3回）に達しました。プレミアムプランにアップグレードすると無制限に使用できます。",
            "upgrade_url": "/billing.html"
        }
    
    if not files:
        raise HTTPException(status_code=400, detail="ファイルがアップロードされていません")
    
    # 以下、既存の処理
    note_id = str(uuid.uuid4())
    notes = []
    
    for file in files:
        contents = await file.read()
        try:
            note_dict = extract_note_from_bytes(contents, file.filename)
            notes.append(note_dict)
        except HTTPException:
            raise                    # 400 系はそのまま
        except OpenAIError as e:
            raise HTTPException(400, f"OCR 失敗: {e}")
        except Exception as e:
            raise HTTPException(500, f"OCR 内部エラー: {e}")    
        NOTE_STORE[note_id] = notes
    
    return {
        "note_id": note_id,
        "notes": notes
    }

# ---------- ② 転写 → 論文 ---------- #
@router.post("/paper")
async def create_paper(
    note_id: str = Body(...),
    edited_pages: List[Dict[str, Any]] = Body(...),
    me: UserDB = role_required(1)  # Depends() を削除
):
    # role_required の結果をチェック
    if isinstance(me, dict) or isinstance(me, Response):
        return me

    if note_id not in NOTE_STORE:
        raise HTTPException(status_code=404, detail="note_id が見つかりません")
    
    # 以下、既存の処理
    # 編集済みの内容で更新
    NOTE_STORE[note_id] = edited_pages
    
    # 複数ページの転写内容を結合
    combined_note = {}
    for page in edited_pages:
        for k, v in page.items():
            # 値を文字列に変換して安全に処理
            v_str = str(v) if v is not None else ""
            if k in combined_note:
                # 既存の値も文字列であることを確認
                existing_str = str(combined_note[k]) if combined_note[k] is not None else ""
                combined_note[k] = existing_str + "\n\n" + v_str
            else:
                combined_note[k] = v_str
    
    try:
        paper_text = compose_paper(combined_note)
        paper_id = str(uuid.uuid4())
        PAPER_STORE[paper_id] = {
            "paper": paper_text,
            "note_dict": combined_note,
            "rewrite_count": 0  # リライト回数カウンター
        }
        return {
            "paper_id": paper_id,
            "paper": paper_text
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"論文生成に失敗しました: {str(e)}")

# ---------- ③ リライト ---------- #
@router.post("/paper/rewrite")
async def rewrite_paper_endpoint(
    paper_id: str = Body(...),
    feedback: str = Body(...),
    me: UserDB = role_required(1)  # Depends() を削除
):
    # role_required の結果をチェック
    if isinstance(me, dict) or isinstance(me, Response):
        return me

    if paper_id not in PAPER_STORE:
        raise HTTPException(status_code=404, detail="paper_id が見つかりません")
    
    paper_data = PAPER_STORE[paper_id]
    
    # リライト回数をチェック
    if paper_data.get("rewrite_count", 0) >= 3:
        raise HTTPException(status_code=400, detail="リライトは3回までです")
    
    try:
        revised = rewrite_paper(
            paper_text=paper_data["paper"],
            feedback=feedback,
            note_dict=paper_data["note_dict"]
        )
        
        # 更新されたデータを保存
        paper_data["paper"] = revised
        paper_data["rewrite_count"] = paper_data.get("rewrite_count", 0) + 1
        
        return {
            "revised_paper": revised,
            "rewrite_count": paper_data["rewrite_count"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"リライトに失敗しました: {str(e)}")

# ---------- ④ 文献自動追加 ---------- #
@router.post("/paper/add_citations")
async def add_citations_endpoint(
    paper_id: str = Body(...),
    paper_text: str = Body(...),
    intro_text: Optional[str] = Body(None),
    discussion_text: Optional[str] = Body(None),
    hits: int = Body(10),
    threshold: int = Body(4),
    max_refs: int = Body(3),
    model: str = Body("o3"),
    author_last_name: Optional[str] = Body(None),
    author_first_name: Optional[str] = Body(None),
    prioritize_author: bool = Body(False),
    require_high_impact: bool = Body(False),
    me: UserDB = role_required(1)  # Depends() を削除
):
    # デバッグ情報を追加
    print(f"DEBUG: add_citations_endpoint called")
    print(f"DEBUG: me type: {type(me)}")

    # role_required の結果をチェック（他のエンドポイントと同様）
    if isinstance(me, dict) or isinstance(me, Response):
        print(f"DEBUG: role_required returned response object: {me}")
        return me

    print(f"DEBUG: Authenticated user: {me.id}")
    # テスト用のpaper_idの場合はチェックをスキップ
    if not paper_id.startswith("test_") and paper_id not in PAPER_STORE:
        raise HTTPException(status_code=404, detail="paper_id が見つかりません")

    # サービス層の関数を呼び出し（タイムアウト付き）
    import asyncio
    try:
        target_sections = {
            "intro": intro_text,
            "discussion": discussion_text
        }

        # 15分でタイムアウト
        result = await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(
                None,
                lambda: add_citations_to_paper(
                    paper_text=paper_text,
                    target_sections=target_sections,
                    hits=hits,
                    threshold=threshold,
                    max_refs=max_refs,
                    model=model,
                    author_last_name=author_last_name,
                    author_first_name=author_first_name,
                    prioritize_author=prioritize_author,
                    require_high_impact=require_high_impact
                )
            ),
            timeout=900  # 15分
        )
        
        # ファイルパスをURLに変換
        ris_filename = os.path.basename(result["ris_path"])
        detail_filename = os.path.basename(result["detail_path"])
        
        return {
            "cited_text": result["cited_text"],
            "numeric_text": result["numeric_text"],
            "ris_url": f"/download/{ris_filename}",
            "detail_json": f"/download/{detail_filename}",
            "references": result["references"]
        }
    except asyncio.TimeoutError:
        raise HTTPException(status_code=408, detail="文献追加処理がタイムアウトしました（15分）。論文の長さを短くするか、後でもう一度お試しください。")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文献追加処理に失敗しました: {str(e)}")

# 引用を元の論文テキストに統合するヘルパー関数
def integrate_citations(full_text, target_text, cited_target):
    # イントロダクションと考察セクションを特定して置換
    sections = ["イントロダクション", "考察"]
    result = full_text
    
    for section in sections:
        # セクション開始位置を検索
        section_start = full_text.find(section)
        if section_start == -1:
            continue
            
        # セクション終了位置を検索（次のセクションまたは文末）
        next_section_start = float('inf')
        for next_section in ["材料と方法", "結果", "考察", "文献リスト"]:
            if next_section == section:
                continue
            pos = full_text.find(next_section, section_start + len(section))
            if pos != -1 and pos < next_section_start:
                next_section_start = pos
        
        if next_section_start == float('inf'):
            next_section_start = len(full_text)
            
        # セクションの内容を抽出
        section_content = full_text[section_start:next_section_start]
        
        # 引用が追加されたセクションの内容を特定
        # 簡易的な方法：元のセクション内容に含まれる文を cited_target から探して置換
        for original_sent in split_japanese_sentences(section_content):
            # 元の文から空白や改行を除去して比較
            clean_original = ''.join(original_sent.split())
            
            # cited_target から対応する引用付き文を探す
            for cited_sent in split_japanese_sentences(cited_target):
                # 引用タグを除去して比較
                clean_cited = ''.join(re.sub(r'\{[^}]+\}', '', cited_sent).split())
                
                if clean_original in clean_cited or clean_cited in clean_original:
                    # 対応する文が見つかったら置換
                    result = result.replace(original_sent, cited_sent)
                    break
    
    return result

# Endnoteタグを引用番号に変換するヘルパー関数
def convert_to_numeric(text):
    ref_map = {}
    counter = 1
    
    def replace_tag(match):
        tag = match.group(1)
        tags = [t.strip() for t in tag.split(';')]
        nums = []
        
        for t in tags:
            if t not in ref_map:
                ref_map[t] = counter
                counter += 1
            nums.append(str(ref_map[t]))
        
        return f"[{','.join(nums)}]"
    
    return re.sub(r'\{([^}]+)\}', replace_tag, text)

# ---------- ⑤ 将来のリライト機能（準備中） ---------- #
@router.post("/paper/analyze_novelty")
async def analyze_novelty_endpoint(
    sentence: str = Body(...),
    me: UserDB = role_required(1)
):
    """
    将来のリライト機能用：文の新規性を分析し、改善提案を生成する
    """
    # role_required の結果をチェック
    if isinstance(me, dict) or isinstance(me, Response):
        return me

    # 現在は基本的な分析のみ実装
    try:
        return {
            "sentence": sentence,
            "analysis": {
                "novelty_score": 3,
                "message": "将来のリライト機能で実装予定",
                "status": "feature_in_development"
            },
            "related_papers_count": 0,
            "status": "analysis_placeholder"
        }

    except Exception as e:
        print(f"DEBUG: Novelty analysis error: {e}")
        return {
            "sentence": sentence,
            "analysis": {"error": str(e)},
            "status": "analysis_failed"
        }
