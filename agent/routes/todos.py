# agent/routes/todos.py

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from starlette.concurrency import run_in_threadpool
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
import urllib.parse

from googleapiclient.discovery import build
from google.oauth2.credentials import Credentials

from agent.db import get_session_local
from agent.models import User as ORMUser, HiddenTodo, TodoItemDB, CategoryDB
from agent.schemas import (
    UserDB,
    TodoItem,
    EventItem,
    ScheduleItem,
    TodosResponse,
    ParticipantsResponse,
    TodoItemUpdate,
)
from agent.auth import role_required
from agent.services.gmail_todo import (
    fetch_recent_emails,
    extract_todos_with_gpt,
    persist_todos,
    fetch_upcoming_events,
    propose_schedule,
    extract_participants_by_topic,
)

from agent.gmail_todos_helper import SCOPES_GMAIL, SCOPES_CALENDAR, fetch_thread_id


router = APIRouter(prefix="/api", tags=["todos"])

@router.get("/todos", response_model=TodosResponse,
                        response_model_by_alias=False)
async def get_todos(
    me_or_resp: UserDB      = role_required(1),   # role_required を使う
    session: AsyncSession   = Depends(get_session_local),
    show_hidden: bool       = Query(False, alias="showHidden"),
    ):
    # role_required が返す Response（ゲスト・プラン不足時の JSONResponse）をそのまま返却
    if isinstance(me_or_resp, Response):
        return me_or_resp
    # plan_required フラグが返ってきたらそのまま JSON を返す
    if isinstance(me_or_resp, dict) and me_or_resp.get("plan_required"):
        return me_or_resp

    # ORM ユーザーを取得
    me = me_or_resp  # UserDB
    db_user = await session.get(ORMUser, me.id)
    if not db_user.gmail_tokens:
        raise HTTPException(403, "Gmail not connected")
    
    # ── ユーザーの非表示リストを取得 ──
    q = await session.execute(
        select(HiddenTodo.task_id)
        .where(HiddenTodo.user_id == me.id)
    )
    hidden_ids = {str(r[0]) for r in q.all()}

    cats = await session.execute(
    select(CategoryDB.name).where(CategoryDB.user_id==me.id)
    )
    user_categories = [row[0] for row in cats.all()]

    # Gmail 連携アカウント(B)のメールアドレスを取得
    def get_gmail_email(tokens):
        creds = Credentials(
            token=tokens["token"],
            refresh_token=tokens.get("refresh_token"),
            token_uri=tokens["token_uri"],
            client_id=tokens["client_id"],
            client_secret=tokens["client_secret"],
            scopes=SCOPES_GMAIL,
        )
        svc = build("gmail", "v1", credentials=creds)
        profile = svc.users().getProfile(userId="me").execute()
        return profile.get("emailAddress")
    
    gmail_address = await run_in_threadpool(get_gmail_email, db_user.gmail_tokens)

    # メール取得→GPT抽出
    emails, thread_map = await run_in_threadpool(fetch_recent_emails, db_user)
    raw_todos        = await extract_todos_with_gpt(emails, gmail_address, user_categories)
    
    # 永続化＆マージ
    persisted = await persist_todos(session, raw_todos)
    await session.commit()
    
    # カレンダーは任意
    if db_user.calendar_tokens:
        raw_events = await run_in_threadpool(fetch_upcoming_events, db_user)
        raw_schedule = await run_in_threadpool(propose_schedule, raw_todos, raw_events)
    else:
        raw_events   = []
        raw_schedule = []   # or 何らかのローカルスケジューリングロジック


    # TodoItem リスト組立
    todos_list: list[TodoItem] = []
    for t in persisted:
        mail_id   = t.get("mail_id", "")
        thread_id = thread_map.get(mail_id)
        auth      = urllib.parse.quote(gmail_address, safe="")
        if thread_id:
            link = f"https://mail.google.com/mail/u/0/?authuser={auth}#all/{thread_id}"
        else:
            enc_mid = urllib.parse.quote(mail_id, safe="")
            link = f"https://mail.google.com/mail/u/0/?authuser={auth}#search/rfc822msgid%3A{enc_mid}"

        is_hidden = t["id"] in hidden_ids
        if not is_hidden or show_hidden:
            todos_list.append(TodoItem(
                id       = t["id"],
                task     = t["task"],
                due      = t.get("due"),
                priority = t.get("priority"),
                mail_id  = mail_id,
                link     = link,
                category = t.get("category"),
                hidden   = is_hidden,
            ))

            
    # EventItem に正規化
    events: list[EventItem] = []
    for e in raw_events:
        events.append(EventItem(**e))

    # ScheduleItem に正規化
    schedule: list[ScheduleItem] = []
    for s in raw_schedule:
        if isinstance(s, dict):
            # task → task_id へ名称変換、欠損値は "" を入れる
            safe = {
                "task_id": s.get("task_id") or s.get("task") or "",
                "start":   s.get("start", ""),
                "end":     s.get("end",   ""),
                "note":    s.get("note"),
            }
            schedule.append(ScheduleItem(**safe))
        else:
            schedule.append(ScheduleItem(task_id="", start="", end="", note=str(s)))

    return TodosResponse(
        todos=todos_list,
        events=events,
        schedule=schedule,
    )
    
@router.patch("/todos/{task_id}/hide", status_code=204)
async def hide_todo(
    task_id: str,
    me_or_resp: UserDB    = role_required(1),
    session: AsyncSession = Depends(get_session_local),
):
    """
    指定したメールIDのToDoを非表示にする。
    """
    # role_required が返す Response をそのまま返却
    if isinstance(me_or_resp, Response):
        return me_or_resp
    # plan_required フラグを含む dict もそのまま返却
    if isinstance(me_or_resp, dict) and me_or_resp.get("plan_required"):
        return me_or_resp

    me = me_or_resp
    # 既存 hidden を消してから追加（重複防止）
    await session.execute(
        delete(HiddenTodo)
        .where(HiddenTodo.user_id == me.id, HiddenTodo.task_id == task_id)
    )
    ht = HiddenTodo(user_id=me.id, task_id=task_id)
    session.add(ht)
    await session.commit()
    return


@router.delete("/todos/{task_id}/hide", status_code=204)
async def unhide_todo(
    task_id: str,
    me_or_resp: UserDB    = role_required(1),
    session: AsyncSession = Depends(get_session_local),
):
    """
    指定したメールIDのToDoを非表示から戻す。
    """
    if isinstance(me_or_resp, Response):
        return me_or_resp
    # plan_required フラグを含む dict もそのまま返却
    if isinstance(me_or_resp, dict) and me_or_resp.get("plan_required"):
        return me_or_resp

    me = me_or_resp
    await session.execute(
        delete(HiddenTodo)
        .where(HiddenTodo.user_id == me.id, HiddenTodo.task_id == task_id)
    )
    await session.commit()
    return

   
@router.patch("/todos/{task_id}", response_model=TodoItem,
              response_model_by_alias=False)
async def update_todo(
    task_id: str,
    data: TodoItemUpdate,
    me_or_resp: UserDB = role_required(1),
    session: AsyncSession = Depends(get_session_local),
):
    if isinstance(me_or_resp, dict) or isinstance(me_or_resp, Response):
        return me_or_resp
    # 1) 自分のレコードかチェック
    stmt = select(TodoItemDB).where(TodoItemDB.task_id==task_id)
    res = await session.execute(stmt)
    item = res.scalar_one_or_none()
    if not item:
        raise HTTPException(404, "Todo not found")
    # 2) カテゴリが空でなければ own categories に存在チェック
    if data.category:
        stmt2 = select(CategoryDB).where(
            CategoryDB.user_id==me_or_resp.id,
            CategoryDB.name==data.category
        )
        r2 = await session.execute(stmt2)
        if not r2.scalar_one_or_none():
            raise HTTPException(400, "Unknown category")
    # 3) 更新
    if data.category is not None:
        item.category = data.category
    await session.commit()
    return TodoItem.from_orm(item)

    
@router.get("/participants", response_model=ParticipantsResponse,
            summary="トピックに関与している人物一覧を返す")
async def get_participants(
    topic: str = Query(..., description="抽出対象のトピック名（例: 自己免疫性疾患）"),
    me_or_resp: UserDB       = role_required(1),  # role_required を使う
    session: AsyncSession    = Depends(get_session_local),
):
    """
    トピックに関与している人物名一覧を返す。
    """
    if isinstance(me_or_resp, Response):
        return me_or_resp
    # plan_required フラグを含む dict もそのまま返却
    if isinstance(me_or_resp, dict) and me_or_resp.get("plan_required"):
        return me_or_resp

    # ORM からユーザー取得
    me = me_or_resp  # UserDB
    db_user = await session.get(ORMUser, me.id)
    if not db_user.gmail_tokens:
        raise HTTPException(status_code=403, detail="Gmail not connected")

    # extract_participants_by_topic は async なので直接 await
    participants = await extract_participants_by_topic(db_user, topic)

    return {
        "topic": topic,
        "participants": participants
    }