from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
import os
import tempfile

router = APIRouter(prefix="/download", tags=["download"])

@router.get("/{filename}")
async def download_file(filename: str):
    """
    一時ディレクトリからファイルをダウンロードするエンドポイント
    """
    # 一時ディレクトリのパス
    tmp_dir = tempfile.gettempdir()
    file_path = os.path.join(tmp_dir, filename)
    
    # ファイルの存在確認
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="ファイルが見つかりません")
    
    # ファイルの拡張子に基づいてメディアタイプを設定
    media_type = None
    if filename.endswith(".ris"):
        media_type = "application/x-research-info-systems"
    elif filename.endswith(".json"):
        media_type = "application/json"
    
    # ファイルをレスポンスとして返す
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type=media_type
    )