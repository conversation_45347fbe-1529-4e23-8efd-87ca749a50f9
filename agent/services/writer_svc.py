from __future__ import annotations
import base64, json, re, textwrap
from pathlib import Path
from textwrap import dedent
from typing import Dict, List, Any

import openai
from fastapi import HTTPException
from agent.pipeline import split_sentences
import tempfile
import os
import uuid

# ============ 共通ユーティリティ ============ #

_MIME_MAP = {
    ".png" : "image/png",
    ".jpg" : "image/jpeg",
    ".jpeg": "image/jpeg",
    ".gif" : "image/gif",
    ".webp": "image/webp",
}

def _img2dataurl(img_bytes: bytes, suffix: str) -> str:
    suffix = suffix.lower()
    if suffix not in _MIME_MAP:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported image type {suffix}. "
                   f"Allowed: {', '.join(_MIME_MAP)}"
        )
    mime = _MIME_MAP[suffix]
    b64  = base64.b64encode(img_bytes).decode()
    return f"data:{mime};base64,{b64}"

# ============ ① 画像 → 転写 ============ #
def extract_note_from_bytes(img_bytes: bytes, fname: str, model: str = "o3", ocr_method: str = "azure") -> Dict:
    """
    画像から実験ノートを転写する（Azure Computer Vision専用）

    Args:
        img_bytes: 画像データ
        fname: ファイル名
        model: 使用するモデル
        ocr_method: OCR方法 ("azure" のみサポート)
    """
    try:
        from agent.ocr_service import ocr_service

        # Azure OCRでレイアウト保持出力を使用（画像前処理有効）
        ocr_result = ocr_service.extract_text_azure(img_bytes, output_format="layout", preprocess=True)

        # 表構造復元機能を適用
        ocr_result = ocr_service.reconstruct_table_structure(ocr_result)

        if ocr_result["success"]:
            # OCRで抽出されたテキストを構造化
            extracted_text = ocr_result["text"]

            # 構造化データがある場合は追加情報を含める
            if "structured_data" in ocr_result or "layout_data" in ocr_result:
                # 構造化OCR結果の処理
                structured_result = structure_lab_notes_enhanced(extracted_text, ocr_result, model)

                # HTML生成
                if ocr_result.get("html_ready"):
                    html_content = ocr_service.generate_html_with_layout(ocr_result)
                    structured_result["html_layout"] = html_content

                # 復元された表構造がある場合
                if "reconstructed_tables" in ocr_result:
                    structured_result["復元された表"] = ocr_result["table_text"]
                    structured_result["表構造データ"] = ocr_result["reconstructed_tables"]
            else:
                # 従来の処理
                structured_result = structure_lab_notes(extracted_text, model)

            # OCR情報を追加
            structured_result["ocr_info"] = {
                "method": ocr_result["method"],
                "confidence": ocr_result["confidence"],
                "detected_languages": ocr_result.get("detected_languages", []),
                "output_format": ocr_result.get("output_format", "layout")
            }

            return structured_result
        else:
            raise Exception(f"Azure OCR failed: {ocr_result.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"Azure OCR failed, falling back to OpenAI: {e}")
        # フォールバック: 従来のOpenAI OCR
        return extract_note_from_bytes_openai(img_bytes, fname, model)

def extract_note_from_bytes_openai(img_bytes: bytes, fname: str, model: str = "o3") -> Dict:
    dataurl = _img2dataurl(img_bytes, Path(fname).suffix)

    sys_msg = {
        "role": "system",
        "content": "You are an assistant that accurately transcribes Japanese handwritten lab notes."
    }
    user_msg = {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": dedent("""\
                    次の手書き実験ノート画像について、以下を JSON で出力してください。
                    入力画像は、科学研究室において記録された実験ノートです。
                    全体を通して、わからない略語は無理に補完しないこと。
                    
                    {
                      "転写内容": 原文をそのまま転写（わからない略語はそのまま）,
                      "ユーザへの質問":  読み取りが不明瞭な箇所があれば、ユーザに質問する。読み取り内容の意味は質問する必要はない。単に文字が読めない場合だけでよい。なければ無理に質問しない。
                    }
                    """)
            },
            {"type": "image_url", "image_url": {"url": dataurl}}
        ]
    }

    rsp = openai.chat.completions.create(
        model=model,
        messages=[sys_msg, user_msg],
        response_format={"type": "json_object"}
    )
    raw = rsp.choices[0].message.content.strip()

    # レスポンス安全パース
    try:
        return json.loads(raw)
    except json.JSONDecodeError:
        m = re.search(r'\{.*\}', raw, re.S)
        if m:
            try:
                return json.loads(m.group())
            except json.JSONDecodeError:
                pass
        preview = raw[:500] + (" ..." if len(raw) > 500 else "")
        raise HTTPException(status_code=500, detail=f"モデル応答を JSON として解釈できません: {preview}")

def structure_lab_notes(extracted_text: str, model: str = "o3") -> Dict:
    """
    OCRで抽出されたテキストを実験ノート構造に整理
    """
    sys_msg = {
        "role": "system",
        "content": "You are an assistant that structures lab notes from OCR text."
    }
    user_msg = {
        "role": "user",
        "content": dedent(f"""\
            以下はOCRで抽出された実験ノートのテキストです。
            このテキストを分析し、以下のJSON形式で構造化してください。

            {extracted_text}

            出力形式:
            {{
              "転写内容": "原文をそのまま転写（わからない略語はそのまま）",
              "ユーザへの質問": "読み取りが不明瞭な箇所があれば質問。なければ空文字列。"
            }}
            """)
    }

    try:
        rsp = openai.chat.completions.create(
            model=model,
            messages=[sys_msg, user_msg],
            response_format={"type": "json_object"}
        )
        raw = rsp.choices[0].message.content.strip()
        return json.loads(raw)
    except Exception as e:
        # エラー時は基本的な構造を返す
        return {
            "転写内容": extracted_text,
            "ユーザへの質問": ""
        }

def structure_lab_notes_enhanced(extracted_text: str, ocr_result: Dict, model: str = "o3") -> Dict:
    """
    構造化OCR結果を活用した実験ノート整理
    """
    sys_msg = {
        "role": "system",
        "content": "You are an assistant that structures lab notes from enhanced OCR data with layout information."
    }

    # 構造化データの情報を含める
    enhanced_info = ""
    if "layout_data" in ocr_result:
        layout_data = ocr_result["layout_data"]
        table_count = sum(1 for item in layout_data if item.get("type") == "table_row")
        enhanced_info = f"\n\n【レイアウト情報】\n- 表形式データ: {table_count}行\n- 総行数: {len(layout_data)}"
    elif "structured_data" in ocr_result:
        structured_data = ocr_result["structured_data"]
        enhanced_info = f"\n\n【構造化情報】\n- ページ数: {structured_data['metadata']['total_pages']}\n- 行数: {structured_data['metadata']['total_lines']}\n- 単語数: {structured_data['metadata']['total_words']}"

    user_msg = {
        "role": "user",
        "content": dedent(f"""\
            以下は位置情報付きOCRで抽出された実験ノートのテキストです。
            レイアウト情報を考慮して、このテキストを分析し、以下のJSON形式で構造化してください。

            {extracted_text}{enhanced_info}

            出力形式:
            {{
              "転写内容": "レイアウトを考慮して整理された内容（表形式は保持）",
              "ユーザへの質問": "読み取りが不明瞭な箇所があれば質問。なければ空文字列。",
              "レイアウト分析": "表や図の配置についての分析結果"
            }}
            """)
    }

    try:
        rsp = openai.chat.completions.create(
            model=model,
            messages=[sys_msg, user_msg],
            response_format={"type": "json_object"}
        )
        raw = rsp.choices[0].message.content.strip()
        result = json.loads(raw)

        # 構造化データを追加
        if "layout_data" in ocr_result:
            result["layout_data"] = ocr_result["layout_data"]
        if "structured_data" in ocr_result:
            result["structured_data"] = ocr_result["structured_data"]

        return result
    except Exception as e:
        # エラー時は基本的な構造を返す
        return {
            "転写内容": extracted_text,
            "ユーザへの質問": "",
            "レイアウト分析": f"構造化処理エラー: {str(e)}"
        }

# ============ ② 論文生成 ============ #
def compose_paper(note_dict: Dict, model: str = "o3") -> str:
    structured = "\n".join(f"<{k}>{v}" for k, v in note_dict.items())
    sys_msg = {"role": "system", "content": "You are a professional scientific writer."}

    user_prompt = dedent(f"""\
        以下は実験ノートの整理済み内容です。
        ----
        {structured}
        ----
        下記のタスクを遂行してください。
        （１）実験ノートが、目的・方法・実験・結果・考察、の５つのセクションで構成されていることを踏まえて、各セクションを文章でまとめてください。
        （２）さらに、その内容（「読み取った内容」）を論文として発表したいと思います。下記の順にステップバイステップで考えて、論文案を完成させてください。
            ステップ１：今回の発見から提唱する主張（メッセージ）を決める。
            ステップ２：メッセージに対応した、論文の「タイトル」を決める。
            ステップ３：メッセージの重要性と新規性、本研究で挑戦した課題、をまとめた「イントロダクション」を書く。
            ステップ４：「材料と方法」ならびに「結果」のセクションを、「読み取った内容」を元にして書く。ここでは、適当な内容を創作することは絶対にしてはいけない。
            ステップ５：全体を踏まえて「考察」をかく。まず最初の段落で今回分かったことを簡潔にまとめるとよい。次の段落で、先行研究との比較を行う。次の段落で、本研究の課題や今後の方向性についても述べる。
            ステップ６：最後に、「要旨」を執筆する。要旨は論文全体を簡潔にまとめたもの。要旨には、細かな数字などは極力含めず、コンセプトの抽出を意識する。最後の結びは必ず「以上の結果は、〜〜〜ということを示唆している」という形式にすること（厳守）。
        全体を通して、「〜を示唆している」は用いてよいが、「〜可能性を示唆している」は避けること
        （３）最後に、作成した文章を次の順番に並び替えて、論文を完成させてください。
            タイトル
            要旨
            イントロダクション
            材料と方法
            結果
            考察
            文献リスト（セクション見出しだけ書いて、中身は空欄で良い。）
            図の説明
        （４）また、結果の出力時には、「中間ステップ」「ステップ１〜６」「実験ノート５セクションの文章化」は出力せず、最終的な論文案だけを出力してください。
        """)

    rsp = openai.chat.completions.create(
        model=model,
        messages=[sys_msg, {"role": "user", "content": user_prompt}]
    )
    result = rsp.choices[0].message.content.strip()

    # 考察セクションの余計な空行を除去
    result = clean_discussion_section(result)

    return result

# ============ ③ リライト ============ #
def rewrite_paper(
    paper_text: str, 
    feedback: str, 
    note_dict: dict,
    model: str = "o3"
    ) -> str:
    structured = "\n".join(f"<{k}>{v}" for k, v in note_dict.items() if k != "transcription")
    sys_msg = {"role": "system", "content": "You are a meticulous scientific editor."}
    user_prompt = dedent(f"""\
        以下は実験ノートの読み取り結果（OCR）です
        --- OCR ---
        {structured}
        --- ここまで ---
        
        以下は論文原稿です。
        --- 原稿 ---
        {paper_text}
        --- ここまで ---

        ユーザーからの修正・要望:
        \"\"\"{feedback}\"\"\"

        上記要望を満たすよう原稿を全面的にリライトし、同じセクション順序を保持してください。
        ただし、初稿時と同様、下記の点には注意してください。
            ルール１：主張（メッセージ）は今回の実験ノートから読み取った内容に基づくこと。
            ルール２：論文の「タイトル」は、メッセージに対応したものであること。
            ルール３：「イントロダクション」は、メッセージの重要性と新規性、本研究で挑戦した課題、をまとめたものであること。
            ルール４：「材料と方法」および「結果」のセクションを、「読み取った内容」を元にして書くこと。ここでは、適当な内容を創作することは絶対にしてはいけない。
            ルール５：「考察」は全体を踏まえ、次の形式が望ましい。まず最初の段落で今回分かったことを簡潔にまとめるとよい。次の段落で、先行研究との比較を行う。次の段落で、本研究の課題や今後の方向性についても述べる。
            ルール６：「要旨」は論文全体を簡潔にまとめ、最後の結びは必ず「以上の結果は、〜〜〜を示唆している」という形式にすること（厳守）。
            ルール７：文献リストはセクション見出しだけ書いて、中身は空欄で良い。
            ルール８：全体を通して、「〜を示唆している」は用いてよいが、「〜可能性を示唆している」は避けること。
        """)
    rsp = openai.chat.completions.create(
        model=model,
        messages=[sys_msg, {"role": "user", "content": user_prompt}]
    )
    result = rsp.choices[0].message.content.strip()

    # 考察セクションの余計な空行を除去
    result = clean_discussion_section(result)

    return result

# ============ ④ 文献自動追加 ============ #
def add_citations_to_paper(paper_text: str, target_sections: Dict[str, str],
                          hits: int = 10, threshold: int = 4, max_refs: int = 3,
                          model: str = "o3", progress_callback=None,
                          author_last_name: str = None, author_first_name: str = None,
                          prioritize_author: bool = False, require_high_impact: bool = False) -> Dict:
    """
    論文のイントロダクションと考察セクションに文献を自動追加する
    既存のpipeline.pyの構造を使用
    
    Args:
        paper_text: 論文全体のテキスト
        target_sections: 処理対象セクション {"intro": "イントロダクションテキスト", "discussion": "考察テキスト"}
        hits: 検索する論文数
        threshold: 引用候補とする最低スコア
        max_refs: 1文あたりの最大引用数
        model: 使用するモデル
        
    Returns:
        Dict: {
            "cited_text": 引用付き論文テキスト,
            "numeric_text": 引用番号形式のテキスト,
            "ris_path": RISファイルパス,
            "detail_path": 詳細レポートパス,
            "references": 参考文献リスト
        }
    """
    if progress_callback:
        progress_callback({"step": "start", "message": "文献追加処理を開始しています..."})

    # 処理対象のテキスト（イントロと考察）
    target_text = ""
    if "intro" in target_sections and target_sections["intro"]:
        target_text += target_sections["intro"] + "\n\n"
    if "discussion" in target_sections and target_sections["discussion"]:
        target_text += target_sections["discussion"]

    if not target_text:
        raise ValueError("処理対象のテキストがありません")

    if progress_callback:
        progress_callback({"step": "prepare", "message": "文献検索の準備中..."})

    # 一時ディレクトリとファイル名の準備
    tmpdir = tempfile.gettempdir()
    base = str(uuid.uuid4())
    prefix = os.path.join(tmpdir, base)

    try:
        if progress_callback:
            progress_callback({"step": "search", "message": "PubMedで文献検索中..."})

        # プログレス通知用のラッパー関数
        def enhanced_progress(info):
            if progress_callback and isinstance(info, dict):
                step = info.get("step", "")
                msg = info.get("msg", info.get("message", ""))

                # より詳細なメッセージに変換
                if step == "keyword":
                    progress_callback({"step": "keyword", "message": f"🔍 {msg}"})
                elif step == "search":
                    progress_callback({"step": "search", "message": f"📚 {msg}"})
                elif step == "select":
                    progress_callback({"step": "select", "message": f"🤖 {msg}"})
                elif step == "sentence":
                    progress_callback({"step": "sentence", "message": f"📝 {msg}"})
                elif step == "integrate":
                    progress_callback({"step": "integrate", "message": f"🔗 {msg}"})
                else:
                    progress_callback({"step": step, "message": msg})

        # 著者優先パイプラインまたは従来パイプラインを使用
        if prioritize_author and author_last_name and author_first_name:
            from agent.pipeline import run_pipeline_with_author_priority
            cited, ris_content, rep_base, det_base, references = run_pipeline_with_author_priority(
                target_text,
                hits,
                threshold,
                max_refs,
                True,  # generate_files=True
                prefix,  # out_prefix
                enhanced_progress,  # 拡張プログレス通知
                author_last_name,
                author_first_name
            )
        else:
            from agent.pipeline import run_pipeline
            cited, ris_content, rep_base, det_base, references = run_pipeline(
                target_text,
                hits,
                threshold,
                max_refs,
                True,  # generate_files=True
                prefix,  # out_prefix
                enhanced_progress  # 拡張プログレス通知
            )

        if progress_callback:
            progress_callback({"step": "process", "message": "文献情報を処理中..."})

        # RISファイルを作成
        ris_path = f"{prefix}.ris"
        with open(ris_path, "w", encoding="utf-8") as f:
            f.write(ris_content)

        if progress_callback:
            progress_callback({"step": "integrate", "message": "論文テキストに引用を統合中..."})

        # 元の論文テキストに引用を統合
        full_cited_text = integrate_citations(paper_text, target_text, cited, references)

        if progress_callback:
            progress_callback({"step": "format", "message": "引用番号形式に変換中..."})

        # 引用番号形式のテキストも生成
        numeric_text = convert_to_numeric(full_cited_text)

        # デバッグ情報を出力
        print(f"DEBUG: references type: {type(references)}")
        print(f"DEBUG: references length: {len(references) if isinstance(references, list) else 'N/A'}")
        if isinstance(references, list) and references:
            print(f"DEBUG: first reference: {references[0]}")

        if progress_callback:
            ref_count = len(references) if isinstance(references, list) else 0
            progress_callback({"step": "complete", "message": f"文献追加完了！{ref_count}件の文献を追加しました。"})

        # メモリ解放: 大きな変数をクリア
        del target_text
        del cited
        if progress_callback:
            del progress_callback

        # ガベージコレクション実行
        import gc
        gc.collect()

        return {
            "cited_text": full_cited_text,
            "numeric_text": numeric_text,
            "ris_path": ris_path,
            "detail_path": f"{det_base}_detail.json",
            "references": references if isinstance(references, list) else []
        }
    except Exception as e:
        # エラーが発生した場合、元のテキストをそのまま返す
        # メモリ解放も実行
        import gc
        gc.collect()

        return {
            "cited_text": paper_text,
            "numeric_text": paper_text,
            "ris_path": "",
            "detail_path": "",
            "references": []
        }

def integrate_citations(full_text: str, target_text: str, cited_target: str, references=None) -> str:
    """
    引用を元の論文テキストに統合する

    Args:
        full_text: 元の論文テキスト全体
        target_text: 処理対象のテキスト（イントロと考察）
        cited_target: 引用が追加されたテキスト

    Returns:
        str: 引用が統合された論文テキスト
    """
    try:
        # デバッグ情報を出力
        print(f"DEBUG: full_text length: {len(full_text)}")
        print(f"DEBUG: target_text length: {len(target_text)}")
        print(f"DEBUG: cited_target length: {len(cited_target)}")
        print(f"DEBUG: cited_target preview: {cited_target[:200]}...")

        # cited_targetが空の場合は元のテキストを返す
        if not cited_target or cited_target.strip() == "":
            print("DEBUG: cited_target is empty, returning original text")
            return full_text

        # 引用が追加されたテキストに引用タグ（{}）が含まれているかチェック
        if "{" not in cited_target:
            print("DEBUG: No citation tags found in cited_target")
            return full_text

        # より単純なアプローチ：target_textの内容をcited_targetで置換
        # target_textが複数セクションを含む場合を考慮

        # セクション別の統合戦略
        # イントロダクションと考察を個別に処理
        result_text = full_text

        # cited_targetをセクション別に分割
        cited_sections = {}
        current_section = None
        current_content = []

        for line in cited_target.split('\n'):
            line_stripped = line.strip()
            if line_stripped in ["イントロダクション", "考察"]:
                if current_section and current_content:
                    cited_sections[current_section] = '\n'.join(current_content)
                current_section = line_stripped
                current_content = []
            elif current_section:
                current_content.append(line)

        # 最後のセクションを追加
        if current_section and current_content:
            cited_sections[current_section] = '\n'.join(current_content)

        print(f"DEBUG: Found cited sections: {list(cited_sections.keys())}")

        # 各セクションを個別に統合
        for section_name, section_content in cited_sections.items():
            print(f"DEBUG: Processing section: {section_name}")

            # セクション見出しを探す
            section_start = result_text.find(section_name)
            if section_start != -1:
                # 次のセクション見出しまでを探す
                next_sections = ["材料と方法", "方法", "結果", "考察", "結論", "謝辞", "参考文献", "文献リスト", "図の説明"]
                section_end = len(result_text)

                for next_sec in next_sections:
                    if next_sec != section_name:  # 自分自身は除外
                        next_pos = result_text.find(next_sec, section_start + len(section_name))
                        if next_pos != -1 and next_pos < section_end:
                            section_end = next_pos

                # 元のセクション内容を取得して段落構造を分析
                original_section = result_text[section_start + len(section_name):section_end].strip()

                # cited_targetの段落構造を保持
                # セクション見出しの後の改行パターンを元のテキストから取得
                section_header_end = section_start + len(section_name)
                next_char_pos = section_header_end
                while next_char_pos < len(result_text) and result_text[next_char_pos] in [' ', '\n']:
                    next_char_pos += 1

                # 元のテキストの改行パターンを保持
                header_spacing = result_text[section_header_end:next_char_pos]
                if not header_spacing:
                    header_spacing = '\n'

                # セクション内容を置換（段落構造を保持、余計な空行を除去）
                cleaned_content = section_content.strip()
                # 連続する空行を単一の空行に変換
                import re
                cleaned_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_content)

                result_text = result_text[:section_start + len(section_name)] + header_spacing + cleaned_content + '\n' + result_text[section_end:]
                print(f"DEBUG: Successfully integrated {section_name} section with preserved paragraph structure")
            else:
                print(f"DEBUG: Section {section_name} not found in full_text")

        # 文献リストセクションにAPA形式の文献一覧を挿入
        if references and isinstance(references, list) and len(references) > 0:
            bibliography_section = generate_bibliography_apa(references)

            # 「文献リスト」または「参考文献」セクションを探す
            ref_section_names = ["文献リスト", "参考文献", "文献"]
            for ref_section_name in ref_section_names:
                ref_section_start = result_text.find(ref_section_name)
                if ref_section_start != -1:
                    # セクション見出しの後の位置を特定
                    section_header_end = ref_section_start + len(ref_section_name)

                    # 次のセクション（図の説明など）までを探す
                    next_sections = ["図の説明", "表の説明", "謝辞", "付録"]
                    section_end = len(result_text)

                    for next_sec in next_sections:
                        next_pos = result_text.find(next_sec, section_header_end)
                        if next_pos != -1 and next_pos < section_end:
                            section_end = next_pos

                    # 文献リストを挿入（見出し前に1行空ける）
                    result_text = result_text[:section_header_end] + '\n\n' + bibliography_section + '\n\n' + result_text[section_end:]
                    print(f"DEBUG: Successfully inserted bibliography into {ref_section_name} section")
                    break

        return result_text

    except Exception as e:
        print(f"DEBUG: Error in integrate_citations: {e}")
        # エラーが発生した場合は元のテキストを返す
        return full_text

def convert_to_numeric(text: str) -> str:
    """
    Endnoteタグを引用番号に変換する
    
    Args:
        text: Endnoteタグ形式のテキスト
        
    Returns:
        str: 引用番号形式のテキスト
    """
    try:
        ref_map = {}
        counter = 1
        
        def replace_tag(match):
            nonlocal counter
            tag = match.group(1)
            tags = [t.strip() for t in tag.split(';')]
            nums = []
            
            for t in tags:
                if t not in ref_map:
                    ref_map[t] = counter
                    counter += 1
                nums.append(str(ref_map[t]))
            
            return f"[{','.join(nums)}]"
        
        return re.sub(r'\{([^}]+)\}', replace_tag, text)
    except Exception as e:
        # エラーが発生した場合は元のテキストを返す
        return text

def generate_bibliography_apa(references: List[Dict]) -> str:
    """
    文献リストをAPA形式で生成する

    Args:
        references: 文献情報のリスト

    Returns:
        str: APA形式の文献リスト
    """
    bibliography_lines = []

    for i, ref in enumerate(references, 1):
        try:
            # 著者情報の処理
            authors = ref.get("authors", [])
            if isinstance(authors, list) and authors:
                # 最初の著者は「姓, 名」、それ以降は「名 姓」
                author_parts = []
                for j, author in enumerate(authors[:6]):  # 最大6名まで
                    if j == 0:
                        # 最初の著者：姓, 名の形式を保持
                        author_parts.append(author)
                    else:
                        # 2番目以降：名 姓に変換を試みる
                        if "," in author:
                            last, first = author.split(",", 1)
                            author_parts.append(f"{first.strip()} {last.strip()}")
                        else:
                            author_parts.append(author)

                if len(authors) > 6:
                    author_str = ", ".join(author_parts) + ", et al."
                else:
                    author_str = ", ".join(author_parts)
            elif isinstance(authors, str) and authors:
                author_str = authors
            else:
                author_str = "Unknown Author"

            # 年の処理
            year = ref.get("year", "n.d.")

            # タイトルの処理
            title = ref.get("title", "Untitled")
            # HTMLタグを除去
            import re
            title = re.sub(r'<[^>]+>', '', title)

            # ジャーナル名の処理
            journal = ref.get("journal", "Unknown Journal")

            # 巻・号・ページの処理
            volume = ref.get("volume", "")
            issue = ref.get("issue", "")
            pages = ref.get("pages", "")

            # APA形式で組み立て
            citation = f"{author_str} ({year}). {title}. *{journal}*"

            if volume:
                citation += f", *{volume}*"
                if issue:
                    citation += f"({issue})"

            if pages:
                citation += f", {pages}"

            citation += "."

            # DOIまたはURLを追加
            doi = ref.get("doi", "")
            url = ref.get("url", "")

            if doi:
                citation += f" https://doi.org/{doi}"
            elif url:
                citation += f" {url}"

            bibliography_lines.append(f"{i}. {citation}")

        except Exception as e:
            # エラーが発生した場合は基本情報のみで作成
            title = ref.get("title", "Unknown Title")
            pmid = ref.get("pmid", "Unknown")
            bibliography_lines.append(f"{i}. {title} (PMID: {pmid})")

    return "\n".join(bibliography_lines)

def clean_discussion_section(text: str) -> str:
    """
    考察セクションの余計な空行を除去する（セクション境界は保持）

    Args:
        text: 論文テキスト

    Returns:
        str: 空行が除去された論文テキスト
    """
    import re

    # 考察セクションを特定（セクション境界を正確に検出）
    discussion_patterns = [
        r'(考察\s*\n)(.*?)(?=\n(?:文献|参考文献|文献リスト|図の説明|表の説明|謝辞|付録))',
        r'(ディスカッション\s*\n)(.*?)(?=\n(?:文献|参考文献|文献リスト|図の説明|表の説明|謝辞|付録))'
    ]

    for pattern in discussion_patterns:
        match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
        if match:
            section_header = match.group(1)
            section_content = match.group(2)

            # 考察セクション内の連続する空行を単一の空行に変換
            cleaned_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', section_content)
            # 先頭の余計な空行を除去
            cleaned_content = cleaned_content.lstrip()
            # 末尾は改行で終わるようにする（次のセクションとの境界を保持）
            if not cleaned_content.endswith('\n'):
                cleaned_content += '\n'

            # 元のテキストを置換（次のセクションとの間に空行を確保）
            original_section = match.group(0)
            new_section = section_header + cleaned_content + '\n'
            text = text.replace(original_section, new_section)
            break

    return text

def parse_paper_sections(text: str) -> Dict[str, Any]:
    """
    論文テキストを階層構造を保持して分割する

    Args:
        text: 論文テキスト

    Returns:
        Dict: 階層構造を持つセクション辞書
    """
    import re
    from typing import Any

    # メインセクションとサブセクションのパターン
    main_sections = [
        "タイトル", "要旨", "イントロダクション", "はじめに", "序論",
        "材料と方法", "方法", "実験方法", "結果", "考察", "ディスカッション",
        "結論", "まとめ", "謝辞", "参考文献", "文献リスト", "文献", "図の説明", "表の説明"
    ]

    # サブセクションのパターン（材料と方法内など）
    sub_section_patterns = [
        "試料", "試薬", "実験材料", "動物", "細胞", "培養",
        "試験系", "実験系", "評価", "測定", "解析", "統計",
        "手順", "プロトコル", "条件"
    ]

    sections = {}

    # メインセクションを検索
    section_positions = []
    for pattern in main_sections:
        matches = list(re.finditer(rf'(?:^|\n)({re.escape(pattern)})\s*(?:\n|$)', text, re.MULTILINE))
        for match in matches:
            section_positions.append({
                'name': pattern,
                'start': match.start(1),
                'end': match.end(),
                'level': 'main'
            })

    # 位置順にソート
    section_positions.sort(key=lambda x: x['start'])

    # 各メインセクションの内容を抽出し、サブセクションも検索
    for i, section in enumerate(section_positions):
        start_pos = section['end']
        # 次のメインセクションの開始位置を終了位置とする
        if i + 1 < len(section_positions):
            end_pos = section_positions[i + 1]['start']
        else:
            end_pos = len(text)

        section_content = text[start_pos:end_pos].strip()

        # サブセクションを検索
        subsections = {}
        sub_positions = []

        for sub_pattern in sub_section_patterns:
            matches = list(re.finditer(rf'(?:^|\n)({re.escape(sub_pattern)})\s*(?:\n|$)', section_content, re.MULTILINE))
            for match in matches:
                sub_positions.append({
                    'name': sub_pattern,
                    'start': match.start(1),
                    'end': match.end()
                })

        if sub_positions:
            # サブセクションがある場合
            sub_positions.sort(key=lambda x: x['start'])

            # 最初のサブセクション前の内容
            if sub_positions[0]['start'] > 0:
                intro_content = section_content[:sub_positions[0]['start']].strip()
                if intro_content:
                    subsections['_intro'] = {
                        'content': intro_content,
                        'paragraphs': intro_content.split('\n\n')
                    }

            # 各サブセクションの内容を抽出
            for j, subsection in enumerate(sub_positions):
                sub_start = subsection['end']
                if j + 1 < len(sub_positions):
                    sub_end = sub_positions[j + 1]['start']
                else:
                    sub_end = len(section_content)

                sub_content = section_content[sub_start:sub_end].strip()
                if sub_content:
                    subsections[subsection['name']] = {
                        'content': sub_content,
                        'paragraphs': sub_content.split('\n\n') if '\n\n' in sub_content else [sub_content]
                    }

            sections[section['name']] = {
                'type': 'structured',
                'subsections': subsections
            }
        else:
            # サブセクションがない場合
            paragraphs = section_content.split('\n\n') if '\n\n' in section_content else [section_content]
            sections[section['name']] = {
                'type': 'simple',
                'content': section_content,
                'paragraphs': paragraphs
            }

    return sections

def format_paper_with_markers(sections: Dict[str, Any]) -> str:
    """
    階層構造セクション辞書を見出し記号付きテキストに変換

    Args:
        sections: 階層構造セクション辞書

    Returns:
        str: 見出し記号付きテキスト
    """
    from typing import Any

    formatted_text = ""

    for section_name, section_data in sections.items():
        formatted_text += f"## {section_name} ##\n"

        if isinstance(section_data, dict):
            if section_data.get('type') == 'structured':
                # 構造化セクション（サブセクション有り）
                subsections = section_data.get('subsections', {})

                # イントロ部分があれば先に出力
                if '_intro' in subsections:
                    intro = subsections['_intro']
                    formatted_text += f"{intro['content']}\n\n"

                # サブセクションを出力
                for sub_name, sub_data in subsections.items():
                    if sub_name != '_intro':
                        formatted_text += f"### {sub_name} ###\n"
                        formatted_text += f"{sub_data['content']}\n\n"

            elif section_data.get('type') == 'simple':
                # シンプルセクション
                formatted_text += f"{section_data['content']}\n\n"
        else:
            # 後方互換性のため
            formatted_text += f"{section_data}\n\n"

    return formatted_text.strip()

def reconstruct_paper_from_sections(sections: Dict[str, Any]) -> str:
    """
    階層構造セクション辞書から元の論文形式に復元

    Args:
        sections: 階層構造セクション辞書

    Returns:
        str: 復元された論文テキスト
    """
    from typing import Any

    reconstructed = ""

    for section_name, section_data in sections.items():
        reconstructed += f"{section_name}\n"

        if isinstance(section_data, dict):
            if section_data.get('type') == 'structured':
                # 構造化セクション
                subsections = section_data.get('subsections', {})

                # イントロ部分
                if '_intro' in subsections:
                    intro = subsections['_intro']
                    reconstructed += f"{intro['content']}\n\n"

                # サブセクション
                for sub_name, sub_data in subsections.items():
                    if sub_name != '_intro':
                        reconstructed += f"{sub_name}\n"
                        reconstructed += f"{sub_data['content']}\n\n"

            elif section_data.get('type') == 'simple':
                # シンプルセクション
                reconstructed += f"{section_data['content']}\n\n"
        else:
            # 後方互換性
            reconstructed += f"{section_data}\n\n"

    return reconstructed.strip()
