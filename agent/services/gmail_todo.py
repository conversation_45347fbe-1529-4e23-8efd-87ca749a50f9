# agent/services/gmail_todo.py

import os, re, json, uuid, base64, datetime, logging, hashlib, asyncio
from typing import Dict, List, Any, Optional
from urllib.parse import quote
from email.utils import getaddresses

from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build

from agent.gmail_todos_helper import fetch_thread_id
from agent.models import TodoItemDB

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from rapidfuzz import fuzz


# OpenAI の非同期クライアントを初期化
from openai import AsyncOpenAI
openai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# OpenAI の同期クライアントを初期化
from openai import OpenAI
sync_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# logger 設定（既に basicConfig で INFO 以上出力されるはず）
logger = logging.getLogger(__name__)

# Gmail API の scope
SCOPES_GMAIL    = ['https://www.googleapis.com/auth/gmail.readonly']
SCOPES_CALENDAR = [    'https://www.googleapis.com/auth/calendar.readonly',
                   'https://www.googleapis.com/auth/calendar.events.readonly',]


# ── テキスト正規化 & ハッシュ化 ───────────────────────────
def normalize_text(text: str) -> str:
    s = text.strip().lower()
    s = re.sub(r'[。\.]+$', '', s)       # 文末句読点除去
    s = re.sub(r'\s+', ' ', s)           # 空白・改行まとめ
    return s

def hash_text(norm: str) -> str:
    return hashlib.sha256(norm.encode('utf-8')).hexdigest()


# ── 永続化＆マージロジック ───────────────────────────────
async def persist_todos(
    session: AsyncSession,
    raw_todos: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    """
    GPT 抽出後の raw_todos を永続化し、
    再取得時も同一 task_id を再利用 or 新規発行して返す。
    """
    # 1) normalize & hash
    for t in raw_todos:
        norm = normalize_text(t['task'])
        t['_norm'] = norm
        t['_hash'] = hash_text(norm)

    # 2) 既存完全一致レコード一括取得
    mail_ids = list({t['mail_id'] for t in raw_todos})
    hashes   = list({t['_hash'] for t in raw_todos})
    stmt = select(TodoItemDB).where(
        TodoItemDB.mail_id.in_(mail_ids),
        TodoItemDB.task_text_hash.in_(hashes)
    )
    res = await session.execute(stmt)
    exist_list = res.scalars().all()
    exist_map = {
        (e.mail_id, e.task_text_hash): e
        for e in exist_list
    }

    persistent: List[Dict[str, Any]] = []

    # 3) 各 raw_todo をマッチ or insert
    for t in raw_todos:
        key = (t['mail_id'], t['_hash'])
        item = exist_map.get(key)

        # 4) 完全一致がなければ同メール内で fuzzy match
        if item is None:
            stmt2 = select(TodoItemDB).where(TodoItemDB.mail_id == t['mail_id'])
            same_mail = (await session.execute(stmt2)).scalars().all()
            for cand in same_mail:
                score = fuzz.ratio(t['_norm'], normalize_text(cand.task_text))
                if score >= 90:
                    item = cand
                    break

        # 5) マッチしなければ新規 insert
        if item is None:
            item = TodoItemDB(
                mail_id        = t['mail_id'],
                task_text      = t['task'],
                task_text_hash = t['_hash'],
                category       = t.get('category'),
                due            = t.get('due'),
                priority       = t.get('priority', 3),
                link           = t.get('link'),
            )
            session.add(item)
            await session.flush()   # item.task_id を取得

        # 6) 永続化された task_id をセットして返却用リストに追加
        persistent.append({
            "id":       str(item.task_id),
            "task":     t['task'],
            "due":      t.get('due'),
            "priority": t.get('priority'),
            "mail_id":  t['mail_id'],
            "link":     t.get('link',''),
            "category": t.get('category'),
        })

    # ※コミットは呼び出し元で行う
    return persistent

def _extract_plain_text(payload) -> str:
    text = ""
    if payload.get("mimeType")=="text/plain" and payload.get("body",{}).get("data"):
        b = payload["body"]["data"]
        text += base64.urlsafe_b64decode(b).decode('utf-8',errors='ignore')
    for part in payload.get("parts",[]):
        text += _extract_plain_text(part)
    return text

def fetch_recent_emails(user) -> tuple[Dict[str,str], Dict[str,str]]:
    """
    Gmail API で過去 7 日以内に受信したメールの
    messageId -> プレーンテキスト本文 を返す（ログ付き）
    """
    # 1) Credentials 組み立て
    tok = user.gmail_tokens
    creds = Credentials(
        token=tok["token"],
        refresh_token=tok.get("refresh_token"),
        token_uri=tok["token_uri"],
        client_id=tok["client_id"],
        client_secret=tok["client_secret"],
        scopes=SCOPES_GMAIL,
    )

    service = build('gmail', 'v1', credentials=creds, cache_discovery=False)
    query = 'in:inbox newer_than:7d'
    logger.info("Gmail fetch query=%r for user %s", query, user.id)

    # 2) メッセージ一覧取得
    resp = service.users().messages().list(
        userId='me',
        q=query
    ).execute()
    messages = resp.get('messages', [])
    logger.info("Gmail API returned %d messages metadata", len(messages))

    
    bodies: Dict[str, str] = {}
    threads: Dict[str, str] = {}
    for m in messages:
        full = service.users().messages().get(
            userId='me',
            id=m['id'],
            format='full'
        ).execute()

        # スレッドIDをキャプチャ
        tid = full.get('threadId')
        threads[m['id']] = tid

        # 本文抽出
        text = _extract_plain_text(full.get('payload', {}))
        if text:
            bodies[m['id']] = text[:5000]
        
    logger.info("Returning %d email bodies", len(bodies))
    return bodies, threads

    
def fetch_upcoming_events(user, days_ahead: int = 7) -> List[Dict]:
    tok = user.calendar_tokens
    creds = Credentials(
        token=tok["token"],
        refresh_token=tok.get("refresh_token"),
        token_uri=tok["token_uri"],
        client_id=tok["client_id"],
        client_secret=tok["client_secret"],
        scopes=SCOPES_CALENDAR,
    )
    service = build('calendar', 'v3', credentials=creds, cache_discovery=False)
    now     = datetime.datetime.utcnow().isoformat() + 'Z'
    future  = (datetime.datetime.utcnow() + datetime.timedelta(days=days_ahead)).isoformat() + 'Z'
    resp    = service.events().list(
        calendarId='primary', timeMin=now, timeMax=future,
        singleEvents=True, orderBy='startTime'
    ).execute()
    out = []
    for ev in resp.get('items', []):
        start = ev['start'].get('dateTime') or ev['start'].get('date')
        end   = ev['end'].get('dateTime')   or ev['end'].get('date')
        out.append({
            'id':      ev['id'],
            'summary': ev.get('summary',''),
            'start':   start,
            'end':     end,
        })
    return out


async def extract_todos_with_gpt(
    email_texts: Dict[str, str],   # {mail_id: body, ...}
    gmail_address: str,             # リンク生成用 authuser
    user_categories: Optional[List[str]] = None,
) -> List[Dict[str, Any]]:
    
    # user_categories が渡されなければ既定の５カテゴリ
    categories_enum = user_categories or ["実験","会議","教育","管理","その他"]
    
    # 最大150通まで処理
    items       = list(email_texts.items())[:150]
    chunk_size  = 5
    chunks      = [dict(items[i:i+chunk_size]) for i in range(0, len(items), chunk_size)]

    system_prompt = f"""
        あなたは最高性能のタスク抽出エージェントです。
        メール本文から『ユーザーが実行すべき具体的アクション』を漏れなく抽出し、
        曖昧なものも候補としてすべて返してください。
        メールからは、タスクと必須フィールドを JSON で抽出してください。
        たとえメールが英語で書かれていても、抽出したタスクは必ず自然な日本語で出力してください。
        メールの本文は１通の受信メールの場合もあれば、複数のやり取りを含む場合もあります。
        メールには他人のタスクも含まれますので、誰のタスクか明確な場合には、括弧書きで担当者名を付けてください。曖昧な場合は括弧書きは不要です。
        
        タスクの分類に関する指示；
        ユーザーは研究者で、 {', '.join(categories_enum)}に関連したタスクを抱えています。
        したがって、抽出したタスクには、 {', '.join(categories_enum)}、のいずれかのカテゴリを必ず付与してください。
        本人のメールアドレスは、{gmail_address} ですので、送信者や受信者（TO）がこのアドレスの場合は、本人に向けたメールである可能性が高いです。
        メールのタスク分類は、単語を抽出して安易に判定するのではなく、全体の文脈を理解して行ってください。
        特に、大学の研究者は、大学での仕事以外にも、ベンチャー企業の経営や、技術的な相談を受けたりすることがあります。
        メールの宛先や送信者のアドレス見て、そのドメイン情報がタスク分類の判断の役に立つこともあるかもしれません。
    
        その他注意事項：
        宣伝等のメールや、メールマガジン・会報等は無視してください。
        「宣伝・広告メール、リサーチジャーナルの投稿依頼」は。日本語・英語のメールを問わず、絶対にタスクに含めないでください。
        返却は JSON のみ、説明文は不要です。
    """

    # v1 形式の関数定義
    functions = [{
        "name": "return_todos",
        "description": "抽出したタスクを返す",
        "parameters": {
            "type": "object",
            "properties": {
                "todos": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "id":       {"type": "string"},
                            "task":     {"type": "string"},
                            "due":      {"type": ["string", "null"]},
                            "priority": {"type": "integer", "enum": [1, 2, 3]},
                            "mail_id":  {"type": "string"},
                            "link":     {"type": "string"},
                            "category": {
                                "type":"string",
                                "enum": categories_enum
                        }
                        },
                        "required": ["id", "task", "priority", "mail_id", "link", "category"]
                    }
                }
            },
            "required": ["todos"]
        }
    }]
    
    
    # 下記を並列タスク数の制限付きで
    CONCURRENCY = 20                          # ← 必要に応じて調整
    sem = asyncio.Semaphore(CONCURRENCY)
    async def process_chunk_limited(chunk):
        async with sem:                      # 同時実行数を制御
            return await process_chunk(chunk)
    
     # １チャンク分を処理する関数
    async def process_chunk(chunk: Dict[str, str]) -> List[Dict[str, Any]]:
        user_prompt = "【メール一覧】\n" + json.dumps(chunk, ensure_ascii=False)
        async def call_gpt() -> Optional[str]:
            try:
                resp = await openai_client.chat.completions.create(
                    model="o4-mini",
                    messages=[
                        {"role":"system", "content":system_prompt},
                        {"role":"user",   "content":user_prompt},
                    ],
                    functions=functions,
                    function_call={"name":"return_todos"},
                    response_format = {"type": "json_object"},
                    max_completion_tokens=3000,
                )
            except Exception as e:
                logger.error("API call failed: %s", e)
                return []
            
            # JSON パース
            msg = resp.choices[0].message
            return (msg.function_call.arguments if getattr(msg, "function_call", None) else msg.content) or ""

        # ① 1回目呼び出し
        raw = await call_gpt()

        # ② 空文字列なら即リトライ
        if not raw.strip():
            logger.warning("GPT returned empty JSON, retrying once")
            raw = await call_gpt()

        if not raw or not raw.strip():
            logger.error("GPT returned empty JSON twice. skipping chunk.")
            return []

        # ③ JSON パース（1回目）
        try:
            todos_raw = json.loads(raw)["todos"]
        except Exception as first_err:
            logger.warning("JSON parse failed once: %s. retrying", first_err)
            raw = await call_gpt()
            try:
                todos_raw = json.loads(raw)["todos"]
            except Exception as second_err:
                logger.error("JSON parse failed twice: %s\nRAW=%s", second_err, raw)
                return []

        # ④ 整形
        fixed: List[Dict[str, Any]] = []
        for t in todos_raw:
            fixed.append({
                "id":       t.get("id") or str(uuid.uuid4()),
                "task":     t.get("task", "")[:100],
                "due":      t.get("due"),
                "priority": int(t.get("priority", 3)),
                "mail_id":  t.get("mail_id", ""),
                "link":     "",
                "category": t.get("category")
            })
        return fixed
    
    # ─ 並列実行。失敗は吸収 ─
    tasks    = [process_chunk_limited(c) for c in chunks]
    results  = await asyncio.gather(*tasks, return_exceptions=True)
    all_tdos: List[Dict[str, Any]] = []

    for r in results:
        if isinstance(r, Exception):
            logger.warning("chunk failed: %s", r)
        else:
            all_tdos.extend(r)

    logger.info("Parsed %d todos", len(all_tdos))
    return all_tdos



def propose_schedule(tasks: List[Dict[str,str]], events: List[Dict[str,Any]]) -> List[Dict[str,str]]:
    """既存イベントを避けつつタスクを組み込んだスケジュール案を返す"""
    system = "あなたは有能なスケジューリングアシスタントです。"
    user_p = (
        "既存イベント:\n" + json.dumps(events, ensure_ascii=False, indent=2)
        + "\n\nタスク一覧:\n" + json.dumps(tasks, ensure_ascii=False, indent=2)
        + "\n\n結果は JSON 配列で返してください。"
    )
    rsp = sync_client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=[{"role":"system","content":system},{"role":"user","content":user_p}],
        temperature=0.2
    )
    try:
        lst = json.loads(rsp.choices[0].message.content)
        return lst if isinstance(lst, list) else []
    except:
        return []
    

# ─── ヘルパー定義 ──────────────────────────
# 敬称削除＆空白詰め
NAME_CLEAN_RE = re.compile(r"[ \u3000\t]*(様|先生|さん|殿|さま|君|くん)$")
def clean_name(name: str) -> str:
    n = NAME_CLEAN_RE.sub("", name.strip())
    return re.sub(r"\s+", "", n)


# From/To/Cc 表示名を抽出
def parse_headers(full_msg: dict) -> List[str]:
    hdrs = {h["name"].lower(): h["value"]
            for h in full_msg.get("payload",{}).get("headers",[])}
    names=[]
    for key in ("from","to","cc"):
        if key in hdrs:
            for real, _ in getaddresses([hdrs[key]]):
                c = clean_name(real)
                if c:
                    names.append(c)
    return names

# 過去７日分を full で取得
def fetch_recent_full_messages(user) -> List[dict]:
    tok = user.gmail_tokens
    creds = Credentials(**tok, scopes=SCOPES_GMAIL)
    svc   = build("gmail","v1",credentials=creds, cache_discovery=False)
    meta  = svc.users().messages().list(userId="me",q="in:inbox newer_than:7d").execute()
    out=[]
    for m in meta.get("messages",[]):
        out.append(
            svc.users().messages().get(
                userId="me",id=m["id"],format="full"
            ).execute()
        )
    return out

# ─── トピック関与者抽出 ──────────────────────────
async def extract_participants_by_topic(
    user,
    topic: str
) -> List[str]:
    # 1) full メッセージ取得
    loop = asyncio.get_event_loop()
    full_msgs = await loop.run_in_executor(None, fetch_recent_full_messages, user)

    sem  = asyncio.Semaphore(5)
    freq = {}

    async def process_one(msg: dict):
        # 2) ヘッダ由来の候補リストを作成
        candidates = parse_headers(msg)
        if not candidates:
            return

        # 3) GPT に本文＋候補リストを渡して関与者だけ抽出
        system_prompt = f"""
            あなたはメール解析のプロです。
            以下のメール本文と候補者リストを参照し、
            トピック「{topic}」に実際に関与している人名のみを返してください。
            候補リストに無い名前は絶対に出さないでください。
            出力は JSON のみ、例:
            {{"involved": ["山田太郎","佐藤花子"]}}
            """
        body = _extract_plain_text(msg.get("payload",{}))[:1500]  # 長い場合は切り詰め
        user_msg = {
            "role":"user",
            "content": json.dumps({
                "body": body,
                "candidates": candidates
            }, ensure_ascii=False)
        }

        async with sem:
            resp = await openai_client.chat.completions.create(
                model="gpt-4.1-mini",  # 環境に合わせて変更
                messages=[
                    {"role":"system", "content": system_prompt},
                    user_msg
                ],
                temperature=0,
                max_tokens=1000,
            )

        # 4) 抽出結果を集計
        try:
            data = json.loads(resp.choices[0].message.content)
            for name in data.get("involved",[]):
                n = clean_name(name)
                if n:
                    freq[n] = freq.get(n,0) + 1
        except Exception:
            logger.exception("Parsing participants failed")

    # 5) 並列実行
    await asyncio.gather(*(process_one(m) for m in full_msgs))


    # 6) 出現頻度2回以上を採用
    result = [n for n, count in freq.items() if count >= 2]
    return sorted(result)