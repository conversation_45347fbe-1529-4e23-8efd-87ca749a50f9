from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build

SCOPES_GMAIL    = ['https://www.googleapis.com/auth/gmail.readonly']
SCOPES_CALENDAR = [    'https://www.googleapis.com/auth/calendar.readonly',
                   'https://www.googleapis.com/auth/calendar.events.readonly',]

# スレッドID取得ヘルパ
def fetch_thread_id(message_id, tokens):
    creds = Credentials(
        token=tokens["token"],
        refresh_token=tokens.get("refresh_token"),
        token_uri=tokens["token_uri"],
        client_id=tokens["client_id"],
        client_secret=tokens["client_secret"],
        scopes=SCOPES_GMAIL,
    )
    svc = build("gmail", "v1", credentials=creds, cache_discovery=False)
    m   = svc.users().messages()\
            .get(userId="me", id=message_id, format="metadata")\
            .execute()
    return m.get("threadId")
