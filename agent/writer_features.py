"""
Writer専用機能
- リライト機能
- 新規性分析
- セクション重複除去
- 文中引用最適化
- 図表説明検出
"""

from typing import List, Dict, Any, Optional
import re
import json


def analyze_research_novelty(sentence: str, related_papers: List[Dict], model: str = "gpt-4.1-mini") -> Dict[str, Any]:
    """
    研究の新規性を分析し、改善提案を生成する
    """
    import openai
    
    if not related_papers:
        return {"novelty_analysis": "関連文献が見つかりませんでした", "rewrite_suggestions": []}
    
    papers_summary = []
    for paper in related_papers[:5]:  # 上位5件を分析
        papers_summary.append(f"- {paper.get('title', '')} ({paper.get('year', '')}): {paper.get('abstract', '')[:200]}...")
    
    system_prompt = """
    あなたは研究論文の新規性評価とリライト提案の専門家です。
    与えられた文と関連文献を分析し、研究の新規性を評価して改善提案を行ってください。
    
    分析項目：
    1. 類似研究の存在確認
    2. 差別化ポイントの特定
    3. 新規性を強調するリライト提案
    4. 研究の位置づけ明確化
    
    出力形式：
    {
        "novelty_score": 1-5の数値,
        "similar_studies": ["類似研究の要約"],
        "differentiation_points": ["差別化ポイント"],
        "rewrite_suggestions": ["リライト提案文"],
        "positioning_advice": "研究の位置づけアドバイス"
    }
    """
    
    user_prompt = f"""
    分析対象文: {sentence}
    
    関連文献:
    {chr(10).join(papers_summary)}
    """
    
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            response_format={"type": "json_object"}
        )
        
        return json.loads(response.choices[0].message.content)
        
    except Exception as e:
        return {
            "novelty_score": 3,
            "error": str(e),
            "rewrite_suggestions": ["分析エラーが発生しました"]
        }


def remove_duplicate_sections(text: str, model: str = "gpt-4.1-mini") -> str:
    """
    重複セクションを除去する
    """
    import openai

    # 問題のあるセクションを事前に除去
    text = re.sub(r'\n[^\n]*（作成中）[^\n]*\n', '\n', text)
    text = re.sub(r'\n文献リスト\s*\n（未記載）\s*\n', '\n', text)
    text = re.sub(r'\n文献リスト\s*\n\s*\n', '\n', text)
    
    system_prompt = """
    あなたは学術論文の構造分析の専門家です。
    与えられたテキストから重複するセクションを特定し、除去してください。

    重複の判定基準：
    1. 同じセクション名が複数回出現
    2. 内容が実質的に同じセクション
    3. 「（作成中）」「（未記載）」などの未完成セクション
    4. 図の説明が複数回出現している場合

    特に注意すべき重複パターン：
    - 「文献リスト」が複数回出現
    - 「図の説明」が重複
    - 空の文献リストセクション

    元のテキストの構造と内容を保持しながら、重複部分のみを除去してください。
    """
    
    user_prompt = f"以下のテキストから重複セクションを除去してください:\n\n{text}"
    
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
        )
        
        return response.choices[0].message.content.strip()
        
    except Exception:
        # エラー時は元のテキストを返す
        return text


def insert_mid_sentence_citations(text: str, model: str = "gpt-4.1-mini") -> str:
    """
    文中引用を適切な位置に挿入する
    """
    import openai
    
    # 文末引用パターンを検出
    citation_pattern = r'([^。]+)(\s*\[[0-9,\s]+\])([。])'
    matches = list(re.finditer(citation_pattern, text))
    
    if not matches:
        return text
    
    system_prompt = """
    あなたは学術論文の引用配置の専門家です。
    文末にある引用を、より適切な文中の位置に移動してください。
    
    移動ルール:
    1. 先行研究への言及部分の直後に引用を配置
    2. 「〜が報告されている」「〜によると」「従来」などの表現の直後
    3. 複合文の場合、引用対象となる部分の直後のみに配置
    4. 本研究の結果や著者の意見部分には引用を配置しない
    
    例:
    変更前: 従来、竜角散成分の抗炎症作用は in vitro で報告されてきたが、CS という in vivo 免疫暴走指標で効果を示した報告はない [9,10]。
    変更後: 従来、竜角散成分の抗炎症作用は in vitro で報告されてきた[9,10]が、CS という in vivo 免疫暴走指標で効果を示した報告はない。
    
    文の意味を変えず、引用位置のみを調整してください。
    """
    
    user_prompt = f"以下のテキストの引用位置を最適化してください:\n\n{text}"
    
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
        )
        
        return response.choices[0].message.content.strip()
        
    except Exception:
        # エラー時は元のテキストを返す
        return text


def is_figure_table_description(sentence: str, model: str = "gpt-4.1-mini") -> bool:
    """
    文が図表説明かどうかを判定する
    """
    import openai
    
    system_prompt = """
    あなたは学術論文の構造分析の専門家です。
    与えられた文が図表の説明文かどうかを判定してください。
    
    図表説明の特徴：
    - 「図1に示すように」「表2から明らかなように」
    - 「Figure 1 shows」「Table 2 indicates」
    - 図表番号への直接的な言及
    - グラフや表の数値の説明
    
    出力形式：
    {"is_figure_description": true/false, "reason": "判定理由"}
    """
    
    user_prompt = f"以下の文が図表説明かどうか判定してください:\n\n{sentence}"
    
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            response_format={"type": "json_object"}
        )
        
        result = json.loads(response.choices[0].message.content)
        return result.get("is_figure_description", False)
        
    except Exception:
        # エラー時は保守的に判定（図表説明ではないと仮定）
        return False


def classify_sentence_type(sentence: str, model: str = "gpt-4.1-mini") -> Dict[str, Any]:
    """
    文の種類を分類し、文献引用の必要性を判定する
    """
    import openai
    
    system_prompt = """
    あなたは学術論文の文章分析の専門家です。
    与えられた文を分析し、種類と引用の必要性を判定してください。
    
    文の分類：
    1. prior_research: 先行研究への言及
    2. background_knowledge: 背景知識の説明
    3. current_findings: 本研究の結果
    4. author_opinion: 著者の意見・解釈
    5. methodology: 研究手法の説明
    6. future_work: 将来の展望
    
    引用の必要性：
    - needed: 引用が必要
    - not_needed: 引用不要
    - conditional: 条件付きで必要
    
    出力形式：
    {
        "sentence_type": "分類",
        "citation_need": "必要性",
        "reason": "判定理由"
    }
    """
    
    user_prompt = f"以下の文を分析してください:\n\n{sentence}"
    
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            response_format={"type": "json_object"}
        )
        
        return json.loads(response.choices[0].message.content)
        
    except Exception:
        # エラー時はデフォルト値を返す
        return {
            "sentence_type": "unknown",
            "citation_need": "needed",
            "reason": "分析エラーのため保守的に判定"
        }


def comprehensive_writer_cleanup(text: str, model: str = "gpt-4.1-mini") -> str:
    """
    Writer専用の包括的なテキスト整理
    """
    # ステップ1: 重複セクションの除去
    text = remove_duplicate_sections(text, model)
    
    # ステップ2: 重複引用番号の除去（コア機能を使用）
    from agent.pipeline import clean_duplicate_citations
    text = clean_duplicate_citations(text, model)
    
    # ステップ3: 文中引用の最適化
    text = insert_mid_sentence_citations(text, model)
    
    return text
