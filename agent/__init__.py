# agent/__init__.py
"""
agent パッケージ
----------------
文献検索エージェントのコア機能を提供します。

主関数
-------
run_pipeline(full_text: str) -> (cited_text: str, ris: str)
process(full_text: str)      -> dict(cited=..., ris=...)
"""

from importlib.metadata import version, PackageNotFoundError
from .pipeline import run_pipeline

# 公開 API（* で import されたときに露出させる名前）
__all__ = ["run_pipeline", "process", "__version__"]

# パッケージバージョン（pip インストールする場合のみ）
try:
    __version__ = version(__name__)
except PackageNotFoundError:
    __version__ = "0.0.0"

def process(full_text: str):
    """
    旧 CLI 互換の薄いラッパー。
    FastAPI からは `run_pipeline` を直接呼んでも OK ですが
    dict で返したい場合はこちらを利用してください。
    """
    cited, ris = run_pipeline(full_text)
    return {"cited": cited, "ris": ris}
