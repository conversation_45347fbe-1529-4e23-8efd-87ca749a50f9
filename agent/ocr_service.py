"""
OCRサービス - Azure Computer Vision API専用
"""

import os
import json
import re
from typing import Dict, List, Any
import requests
import time
import io
from PIL import Image, ImageEnhance, ImageOps
import numpy as np


class OCRService:
    def __init__(self):
        self.azure_available = False

        # Azure Computer Vision APIの初期化
        try:
            self.azure_endpoint = os.getenv('AZURE_COMPUTER_VISION_ENDPOINT')
            self.azure_key = os.getenv('AZURE_COMPUTER_VISION_KEY')
            if self.azure_endpoint and self.azure_key:
                self.azure_available = True
                print("Azure Computer Vision API initialized successfully")
            else:
                print("Azure Computer Vision API credentials not found")
                raise Exception("Azure Computer Vision API credentials are required")
        except Exception as e:
            print(f"Azure Computer Vision API initialization failed: {e}")
            raise

    def preprocess_image_for_ocr(self, image_data: bytes) -> bytes:
        """
        MacのPhotoアプリの調整を再現して画像を前処理
        Brilliance, Exposure, Brightness, Contrastを最大化
        """
        try:
            # バイトデータからPIL Imageを作成
            image = Image.open(io.BytesIO(image_data))

            # RGBAの場合はRGBに変換
            if image.mode == 'RGBA':
                # 白背景で合成
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1])  # アルファチャンネルをマスクとして使用
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # 1. Brightness（明度）を上げる
            brightness_enhancer = ImageEnhance.Brightness(image)
            image = brightness_enhancer.enhance(1.5)  # 50%増加

            # 2. Contrast（コントラスト）を上げる
            contrast_enhancer = ImageEnhance.Contrast(image)
            image = contrast_enhancer.enhance(2.0)  # 100%増加

            # 3. Exposure（露出）効果を再現（ガンマ補正）
            # NumPy配列に変換してガンマ補正
            img_array = np.array(image)
            gamma = 0.7  # ガンマ値を下げて明るくする
            img_array = np.power(img_array / 255.0, gamma) * 255.0
            img_array = np.clip(img_array, 0, 255).astype(np.uint8)
            image = Image.fromarray(img_array)

            # 4. Brilliance（輝度）効果を再現（シャープネス + 彩度調整）
            # シャープネスを上げる
            sharpness_enhancer = ImageEnhance.Sharpness(image)
            image = sharpness_enhancer.enhance(1.3)  # 30%増加

            # 彩度を少し下げて文字を際立たせる
            color_enhancer = ImageEnhance.Color(image)
            image = color_enhancer.enhance(0.8)  # 20%減少

            # 5. 追加の手書き文字最適化
            # ノイズ除去のための軽いぼかし後にシャープ化
            image = image.filter(Image.SMOOTH_MORE)
            image = image.filter(Image.SHARPEN)

            # バイトデータに変換して返す
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='PNG', optimize=True)
            return output_buffer.getvalue()

        except Exception as e:
            print(f"画像前処理エラー: {e}")
            # エラーの場合は元の画像データを返す
            return image_data

    def extract_text_azure(self, image_data: bytes, output_format: str = "structured", preprocess: bool = True) -> Dict[str, Any]:
        """
        Azure Computer Vision APIを使用してOCR処理（手書き特化）
        output_format: "text" (従来), "structured" (JSON構造化), "layout" (レイアウト保持)
        preprocess: 画像前処理を行うかどうか
        """
        if not self.azure_available:
            raise Exception("Azure Computer Vision API is not available")

        try:
            # 画像前処理を実行
            if preprocess:
                print("🖼️ 画像前処理を実行中...")
                image_data = self.preprocess_image_for_ocr(image_data)
            # Read APIを使用（手書き文字に最適）
            read_url = f"{self.azure_endpoint}/vision/v3.2/read/analyze"

            headers = {
                'Ocp-Apim-Subscription-Key': self.azure_key,
                'Content-Type': 'application/octet-stream'
            }

            params = {
                'language': 'ja',  # 日本語を指定
                'readingOrder': 'natural'  # 自然な読み順
            }

            # 読み取り開始
            response = requests.post(read_url, headers=headers, params=params, data=image_data)
            response.raise_for_status()

            # 結果取得用のURL
            operation_url = response.headers["Operation-Location"]

            # 結果を待機
            max_attempts = 30
            for attempt in range(max_attempts):
                time.sleep(1)
                result_response = requests.get(operation_url, headers={'Ocp-Apim-Subscription-Key': self.azure_key})
                result_response.raise_for_status()
                result = result_response.json()

                if result["status"] == "succeeded":
                    break
                elif result["status"] == "failed":
                    raise Exception("Azure OCR processing failed")
            else:
                raise Exception("Azure OCR timeout")

            # 出力形式に応じて処理
            if output_format == "structured":
                return self._process_azure_structured(result)
            elif output_format == "layout":
                return self._process_azure_layout(result)
            else:
                return self._process_azure_text(result)

        except Exception as e:
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "method": "azure_computer_vision",
                "error": str(e)
            }

    def _process_azure_text(self, result: Dict) -> Dict[str, Any]:
        """従来のテキスト形式で処理"""
        extracted_lines = []
        total_confidence = 0
        confidence_count = 0

        if "analyzeResult" in result and "readResults" in result["analyzeResult"]:
            for page in result["analyzeResult"]["readResults"]:
                for line in page["lines"]:
                    extracted_lines.append(line["text"])
                    if "confidence" in line:
                        total_confidence += line["confidence"]
                        confidence_count += 1

        full_text = "\n".join(extracted_lines)
        average_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.9

        return {
            "success": True,
            "text": full_text,
            "confidence": average_confidence,
            "method": "azure_computer_vision",
            "detected_languages": self._detect_languages(full_text),
            "line_count": len(extracted_lines)
        }

    def _process_azure_structured(self, result: Dict) -> Dict[str, Any]:
        """JSON構造化形式で処理（位置情報保持）"""
        structured_data = {
            "pages": [],
            "metadata": {
                "total_pages": 0,
                "total_lines": 0,
                "total_words": 0
            }
        }

        total_confidence = 0
        confidence_count = 0
        all_text_lines = []

        if "analyzeResult" in result and "readResults" in result["analyzeResult"]:
            structured_data["metadata"]["total_pages"] = len(result["analyzeResult"]["readResults"])

            for page_idx, page in enumerate(result["analyzeResult"]["readResults"]):
                page_data = {
                    "page_number": page_idx + 1,
                    "width": page.get("width", 0),
                    "height": page.get("height", 0),
                    "lines": [],
                    "regions": []
                }

                # 行レベルの情報
                for line in page["lines"]:
                    line_data = {
                        "text": line["text"],
                        "bounding_box": line.get("boundingBox", []),
                        "confidence": line.get("confidence", 0.9),
                        "words": []
                    }

                    # 単語レベルの情報
                    for word in line.get("words", []):
                        word_data = {
                            "text": word["text"],
                            "bounding_box": word.get("boundingBox", []),
                            "confidence": word.get("confidence", 0.9)
                        }
                        line_data["words"].append(word_data)

                        if "confidence" in word:
                            total_confidence += word["confidence"]
                            confidence_count += 1

                    page_data["lines"].append(line_data)
                    all_text_lines.append(line["text"])
                    structured_data["metadata"]["total_lines"] += 1
                    structured_data["metadata"]["total_words"] += len(line.get("words", []))

                # 領域検出（表やセクションの推定）
                page_data["regions"] = self._detect_regions(page_data["lines"])
                structured_data["pages"].append(page_data)

        full_text = "\n".join(all_text_lines)
        average_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.9

        return {
            "success": True,
            "text": full_text,
            "structured_data": structured_data,
            "confidence": average_confidence,
            "method": "azure_computer_vision_structured",
            "detected_languages": self._detect_languages(full_text),
            "output_format": "structured"
        }

    def _process_azure_layout(self, result: Dict) -> Dict[str, Any]:
        """レイアウト保持形式で処理（boundingBox活用）"""
        layout_data = []
        total_confidence = 0
        confidence_count = 0
        all_text_lines = []
        page_dimensions = []

        if "analyzeResult" in result and "readResults" in result["analyzeResult"]:
            for page_idx, page in enumerate(result["analyzeResult"]["readResults"]):
                # ページ寸法を記録
                page_width = page.get("width", 1000)
                page_height = page.get("height", 1000)
                page_dimensions.append({"width": page_width, "height": page_height})

                # 提案に従ってboundingBoxを活用
                lines_with_coords = []
                for line in page["lines"]:
                    bbox = line.get("boundingBox", [])
                    if len(bbox) >= 8:  # [x1,y1,x2,y2,x3,y3,x4,y4]
                        # 座標を正規化（0-1の範囲）
                        normalized_bbox = [
                            bbox[i] / page_width if i % 2 == 0 else bbox[i] / page_height
                            for i in range(len(bbox))
                        ]

                        lines_with_coords.append({
                            "text": line["text"],
                            "boundingBox": bbox,
                            "normalized_bbox": normalized_bbox,
                            "confidence": line.get("confidence", 0.9),
                            "page": page_idx
                        })

                        if "confidence" in line:
                            total_confidence += line["confidence"]
                            confidence_count += 1

                # Y座標でクラスタリング（提案のTIPS実装）
                clustered_lines = self._cluster_lines_by_y_coordinate(lines_with_coords)

                # 各クラスタ内でX座標ソート
                for cluster in clustered_lines:
                    sorted_cluster = sorted(cluster, key=lambda x: min(x["boundingBox"][0::2]))

                    if len(sorted_cluster) == 1:
                        # 単一行
                        line = sorted_cluster[0]
                        layout_data.append({
                            "type": "line",
                            "text": line["text"],
                            "boundingBox": line["boundingBox"],
                            "normalized_bbox": line["normalized_bbox"],
                            "confidence": line["confidence"],
                            "page": line["page"]
                        })
                        all_text_lines.append(line["text"])
                    else:
                        # 表形式（複数列）
                        cells = [line["text"] for line in sorted_cluster]
                        layout_data.append({
                            "type": "table_row",
                            "text": " | ".join(cells),
                            "cells": cells,
                            "cell_coords": [line["boundingBox"] for line in sorted_cluster],
                            "normalized_coords": [line["normalized_bbox"] for line in sorted_cluster],
                            "confidence": sum(line["confidence"] for line in sorted_cluster) / len(sorted_cluster),
                            "page": sorted_cluster[0]["page"]
                        })
                        all_text_lines.append(" | ".join(cells))

        # HTML/PDF生成用の座標データを含める
        formatted_text = self._format_layout_text_with_coords(layout_data)
        average_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.9

        return {
            "success": True,
            "text": formatted_text,
            "layout_data": layout_data,
            "page_dimensions": page_dimensions,
            "confidence": average_confidence,
            "method": "azure_computer_vision_layout_enhanced",
            "detected_languages": self._detect_languages(formatted_text),
            "output_format": "layout_with_coordinates",
            "html_ready": True,  # HTML生成可能フラグ
            "pdf_ready": True    # PDF生成可能フラグ
        }

    def _detect_regions(self, lines: List[Dict]) -> List[Dict]:
        """行データから領域（表、セクションなど）を検出（実験ノート表形式特化）"""
        regions = []

        # 表構造の高精度検出
        table_regions = self._detect_table_structures(lines)
        regions.extend(table_regions)

        # 通常のテキスト領域
        non_table_lines = [line for line in lines if not self._is_in_table(line, table_regions)]
        for line in non_table_lines:
            regions.append({
                "type": "text_line",
                "lines": [line],
                "cell_count": 1
            })

        return regions

    def _detect_table_structures(self, lines: List[Dict]) -> List[Dict]:
        """実験ノート特化の表構造検出"""
        tables = []

        # 1. 表のヘッダー検出（Pack, No., 時刻など）
        header_patterns = [
            r'Pack|No\.|時刻|判定|群|Group',
            r'\d+st|\d+nd|\d+rd|\d+th',  # 1st, 2nd, 3rd
            r'CS|Exp|実験|観察'
        ]

        potential_headers = []
        for line in lines:
            text = line.get("text", "")
            for pattern in header_patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    potential_headers.append(line)
                    break

        # 2. ヘッダー周辺の表構造を分析
        for header in potential_headers:
            table_structure = self._analyze_table_around_header(header, lines)
            if table_structure:
                tables.append(table_structure)

        # 3. 罫線や区切り文字による表検出
        separator_tables = self._detect_tables_by_separators(lines)
        tables.extend(separator_tables)

        return tables

    def _analyze_table_around_header(self, header: Dict, all_lines: List[Dict]) -> Dict:
        """ヘッダー周辺の表構造を分析"""
        header_bbox = header.get("bounding_box", [])
        if len(header_bbox) < 4:
            return None

        header_y = (header_bbox[1] + header_bbox[3]) / 2

        # ヘッダーの下にある行を収集（表のデータ行候補）
        data_rows = []
        y_threshold = 100  # ピクセル単位の閾値

        for line in all_lines:
            line_bbox = line.get("bounding_box", [])
            if len(line_bbox) >= 4:
                line_y = (line_bbox[1] + line_bbox[3]) / 2
                if header_y < line_y < header_y + y_threshold:
                    data_rows.append(line)

        if len(data_rows) < 2:  # 最低2行は必要
            return None

        # 列の配置を分析
        columns = self._detect_column_structure(data_rows)

        if len(columns) >= 2:  # 最低2列は必要
            return {
                "type": "table",
                "header": header,
                "data_rows": data_rows,
                "columns": columns,
                "cell_count": len(columns)
            }

        return None

    def _detect_column_structure(self, rows: List[Dict]) -> List[Dict]:
        """行データから列構造を検出"""
        # 各行のテキスト要素をX座標でクラスタリング
        all_elements = []

        for row in rows:
            # 行内の単語を分析（スペースや特殊文字で分割）
            text = row.get("text", "")
            bbox = row.get("bounding_box", [])

            # 時刻パターン（HH:MM）、数値パターン（N/N）、文字列を分離
            elements = self._split_table_elements(text, bbox)
            all_elements.extend(elements)

        # X座標でクラスタリングして列を特定
        columns = self._cluster_by_x_coordinate(all_elements)

        return columns

    def _split_table_elements(self, text: str, bbox: List[float]) -> List[Dict]:
        """表のセル内容を要素に分割"""
        elements = []

        # 時刻パターン（16:35, 19:22など）
        time_pattern = r'\d{1,2}:\d{2}'
        # 分数パターン（0/5, 1/5など）
        fraction_pattern = r'\d+/\d+'
        # 数値パターン
        number_pattern = r'\d+\.?\d*'

        # パターンマッチングで要素を抽出
        patterns = [
            (time_pattern, "time"),
            (fraction_pattern, "fraction"),
            (number_pattern, "number")
        ]

        remaining_text = text
        x_start = bbox[0] if len(bbox) > 0 else 0

        for pattern, element_type in patterns:
            matches = list(re.finditer(pattern, remaining_text))
            for match in matches:
                # 概算のX座標を計算
                char_width = (bbox[2] - bbox[0]) / len(text) if len(text) > 0 else 10
                element_x = x_start + match.start() * char_width

                elements.append({
                    "text": match.group(),
                    "type": element_type,
                    "x_position": element_x,
                    "confidence": 0.9
                })

                # マッチした部分を除去
                remaining_text = remaining_text.replace(match.group(), " ", 1)

        # 残りのテキスト（文字列）
        remaining_text = remaining_text.strip()
        if remaining_text and remaining_text not in ["-", "‐", ""]:
            elements.append({
                "text": remaining_text,
                "type": "text",
                "x_position": x_start,
                "confidence": 0.8
            })

        return elements

    def _cluster_by_x_coordinate(self, elements: List[Dict], threshold: float = 50) -> List[Dict]:
        """X座標でクラスタリングして列を特定"""
        if not elements:
            return []

        # X座標でソート
        sorted_elements = sorted(elements, key=lambda x: x["x_position"])

        columns = []
        current_column = [sorted_elements[0]]

        for i in range(1, len(sorted_elements)):
            current_x = sorted_elements[i]["x_position"]
            prev_x = sorted_elements[i-1]["x_position"]

            if abs(current_x - prev_x) <= threshold:
                current_column.append(sorted_elements[i])
            else:
                columns.append({
                    "elements": current_column,
                    "x_center": sum(e["x_position"] for e in current_column) / len(current_column),
                    "column_type": self._determine_column_type(current_column)
                })
                current_column = [sorted_elements[i]]

        # 最後の列を追加
        columns.append({
            "elements": current_column,
            "x_center": sum(e["x_position"] for e in current_column) / len(current_column),
            "column_type": self._determine_column_type(current_column)
        })

        return columns

    def _determine_column_type(self, elements: List[Dict]) -> str:
        """列の内容から列タイプを判定"""
        types = [e["type"] for e in elements]

        if "time" in types:
            return "time_column"
        elif "fraction" in types:
            return "score_column"
        elif "number" in types:
            return "number_column"
        else:
            return "text_column"

    def _detect_tables_by_separators(self, lines: List[Dict]) -> List[Dict]:
        """区切り文字や罫線による表検出"""
        tables = []

        # 罫線パターン（─、|、＋など）
        separator_patterns = [
            r'[─━−‐-]{3,}',  # 水平線
            r'[│┃|]{1,}',     # 垂直線
            r'[┌┐└┘├┤┬┴┼]'   # 罫線文字
        ]

        separator_lines = []
        for line in lines:
            text = line.get("text", "")
            for pattern in separator_patterns:
                if re.search(pattern, text):
                    separator_lines.append(line)
                    break

        # 区切り線に挟まれた領域を表として認識
        if len(separator_lines) >= 2:
            # 簡単な実装：最初と最後の区切り線の間を表とする
            first_sep = separator_lines[0]
            last_sep = separator_lines[-1]

            first_y = (first_sep.get("bounding_box", [0, 0, 0, 0])[1] +
                      first_sep.get("bounding_box", [0, 0, 0, 0])[3]) / 2
            last_y = (last_sep.get("bounding_box", [0, 0, 0, 0])[1] +
                     last_sep.get("bounding_box", [0, 0, 0, 0])[3]) / 2

            table_rows = []
            for line in lines:
                line_y = (line.get("bounding_box", [0, 0, 0, 0])[1] +
                         line.get("bounding_box", [0, 0, 0, 0])[3]) / 2
                if first_y < line_y < last_y and line not in separator_lines:
                    table_rows.append(line)

            if table_rows:
                tables.append({
                    "type": "separated_table",
                    "separator_lines": separator_lines,
                    "data_rows": table_rows,
                    "cell_count": len(table_rows)
                })

        return tables

    def _is_in_table(self, line: Dict, table_regions: List[Dict]) -> bool:
        """行が表の一部かどうかを判定"""
        for table in table_regions:
            if "data_rows" in table and line in table["data_rows"]:
                return True
            if "header" in table and line == table["header"]:
                return True
        return False

    def _group_lines_by_position(self, lines: List[Dict]) -> List[List[Dict]]:
        """位置に基づいて行をグループ化"""
        if not lines:
            return []

        groups = []
        current_group = [lines[0]]

        for i in range(1, len(lines)):
            current_line = lines[i]
            prev_line = lines[i-1]

            # Y座標の差を計算
            curr_y = current_line.get("boundingBox", [0, 0])[1] if len(current_line.get("boundingBox", [])) > 1 else 0
            prev_y = prev_line.get("boundingBox", [0, 0])[1] if len(prev_line.get("boundingBox", [])) > 1 else 0

            # 同じ行とみなす閾値（ピクセル）
            if abs(curr_y - prev_y) < 20:
                current_group.append(current_line)
            else:
                groups.append(current_group)
                current_group = [current_line]

        groups.append(current_group)
        return groups

    def _format_layout_text(self, layout_data: List[Dict]) -> str:
        """レイアウトデータからフォーマットされたテキストを生成"""
        formatted_lines = []

        for item in layout_data:
            if item["type"] == "line":
                formatted_lines.append(item["text"])
            elif item["type"] == "table_row":
                # 表形式で整列
                cells = item.get("cells", [])
                if len(cells) > 1:
                    # セルを適切な幅で整列
                    max_width = max(len(cell) for cell in cells) if cells else 10
                    formatted_row = " | ".join(cell.ljust(max_width) for cell in cells)
                    formatted_lines.append(formatted_row)
                else:
                    formatted_lines.append(item["text"])

        return "\n".join(formatted_lines)

    def _cluster_lines_by_y_coordinate(self, lines: List[Dict], threshold: float = 0.02) -> List[List[Dict]]:
        """Y座標でクラスタリング（提案のTIPS実装）"""
        if not lines:
            return []

        # Y座標の中央値で行をソート
        sorted_lines = sorted(lines, key=lambda x: (x["normalized_bbox"][1] + x["normalized_bbox"][5]) / 2)

        clusters = []
        current_cluster = [sorted_lines[0]]

        for i in range(1, len(sorted_lines)):
            current_y = (sorted_lines[i]["normalized_bbox"][1] + sorted_lines[i]["normalized_bbox"][5]) / 2
            prev_y = (sorted_lines[i-1]["normalized_bbox"][1] + sorted_lines[i-1]["normalized_bbox"][5]) / 2

            # 動的閾値：表形式データの場合は狭く設定
            dynamic_threshold = self._calculate_dynamic_threshold(current_cluster, sorted_lines[i], threshold)

            # Y座標の差が閾値以下なら同じクラスタ
            if abs(current_y - prev_y) <= dynamic_threshold:
                current_cluster.append(sorted_lines[i])
            else:
                clusters.append(current_cluster)
                current_cluster = [sorted_lines[i]]

        clusters.append(current_cluster)
        return clusters

    def _calculate_dynamic_threshold(self, current_cluster: List[Dict], new_line: Dict, base_threshold: float) -> float:
        """表構造を考慮した動的閾値を計算"""
        # 表形式データの特徴を検出
        cluster_texts = [line.get("text", "") for line in current_cluster]
        new_text = new_line.get("text", "")

        # 表形式パターンの検出
        table_patterns = [r'\d+/\d+', r'\d{1,2}:\d{2}', r'^\d+$', r'Pack|CS|Exp|No\.']

        table_score = 0
        all_texts = cluster_texts + [new_text]

        for text in all_texts:
            for pattern in table_patterns:
                if re.search(pattern, text):
                    table_score += 1
                    break

        # 表形式の可能性が高い場合は閾値を狭く
        if len(all_texts) > 0 and table_score / len(all_texts) > 0.5:
            return base_threshold * 0.5  # 表形式：閾値を半分に
        else:
            return base_threshold  # 通常テキスト：デフォルト閾値

    def _format_layout_text_with_coords(self, layout_data: List[Dict]) -> str:
        """座標情報を含むレイアウトテキスト生成"""
        formatted_lines = []

        for item in layout_data:
            if item["type"] == "line":
                formatted_lines.append(item["text"])
            elif item["type"] == "table_row":
                # 表形式で整列（提案の通り）
                cells = item.get("cells", [])
                if len(cells) > 1:
                    # セルを適切な幅で整列
                    max_width = max(len(cell) for cell in cells) if cells else 10
                    formatted_row = " | ".join(cell.ljust(max_width) for cell in cells)
                    formatted_lines.append(formatted_row)
                else:
                    formatted_lines.append(item["text"])

        return "\n".join(formatted_lines)

    def generate_html_with_layout(self, ocr_result: Dict, original_image_path: str = None) -> str:
        """座標情報を使ってHTML生成（提案の実装）"""
        if not ocr_result.get("html_ready") or "layout_data" not in ocr_result:
            return "<p>レイアウト情報が利用できません</p>"

        layout_data = ocr_result["layout_data"]
        page_dimensions = ocr_result.get("page_dimensions", [{"width": 1000, "height": 1000}])

        html_parts = []
        html_parts.append("""
        <div style="position: relative; width: 100%; max-width: 800px; margin: 0 auto;">
        """)

        if original_image_path:
            html_parts.append(f"""
            <img src="{original_image_path}" style="width: 100%; height: auto; position: absolute; top: 0; left: 0; z-index: 1;">
            """)

        # 各テキスト要素を絶対位置で配置
        for item in layout_data:
            if item["type"] == "line":
                bbox = item["normalized_bbox"]
                x_percent = bbox[0] * 100
                y_percent = bbox[1] * 100

                html_parts.append(f"""
                <div style="position: absolute; left: {x_percent}%; top: {y_percent}%;
                           z-index: 2; background: rgba(255,255,255,0.8);
                           font-size: 12px; white-space: nowrap;">
                    {item["text"]}
                </div>
                """)
            elif item["type"] == "table_row":
                # 表の各セルを個別に配置
                for i, (cell_text, cell_bbox) in enumerate(zip(item["cells"], item["normalized_coords"])):
                    x_percent = cell_bbox[0] * 100
                    y_percent = cell_bbox[1] * 100

                    html_parts.append(f"""
                    <div style="position: absolute; left: {x_percent}%; top: {y_percent}%;
                               z-index: 2; background: rgba(255,255,255,0.8);
                               font-size: 12px; border: 1px solid #ccc; padding: 2px;">
                        {cell_text}
                    </div>
                    """)

        html_parts.append("</div>")
        return "".join(html_parts)

    def extract_text_document_intelligence(self, image_data: bytes) -> Dict[str, Any]:
        """
        Azure AI Document Intelligence (旧Form Recognizer) を使用
        提案のprebuilt-layoutモデルを実装
        """
        # TODO: Phase 2で実装
        # from azure.ai.formrecognizer import DocumentAnalysisClient
        # from azure.core.credentials import AzureKeyCredential

        return {
            "success": False,
            "text": "",
            "confidence": 0,
            "method": "document_intelligence",
            "error": "Document Intelligence not yet implemented. Use layout mode for now."
        }

    def generate_searchable_pdf(self, ocr_result: Dict, original_image_path: str, output_path: str) -> bool:
        """
        提案の「画像+透明文字」PDF生成
        """
        # TODO: Phase 3で実装
        # from reportlab.pdfgen import canvas
        # from PIL import Image

        return False

    def reconstruct_table_structure(self, ocr_result: Dict) -> Dict[str, Any]:
        """
        実験ノート表形式の構造復元（座標ベース改良版）
        """
        if "layout_data" not in ocr_result:
            return ocr_result

        layout_data = ocr_result["layout_data"]

        # 座標ベースの表構造復元
        coordinate_based_tables = self._reconstruct_tables_by_coordinates(layout_data)

        # 従来の方法も併用
        text_based_tables = []
        for item in layout_data:
            if item.get("type") == "table" or self._looks_like_table_data(item):
                reconstructed_table = self._reconstruct_single_table(item)
                if reconstructed_table:
                    text_based_tables.append(reconstructed_table)

        # 座標ベースの結果を優先し、不足分を従来方法で補完
        all_tables = coordinate_based_tables + text_based_tables

        # 重複除去
        unique_tables = self._deduplicate_tables(all_tables)

        # 復元された表をテキストに変換
        if unique_tables:
            table_text = self._format_reconstructed_tables(unique_tables)

            # 元のOCR結果に追加
            enhanced_result = ocr_result.copy()
            enhanced_result["reconstructed_tables"] = unique_tables
            enhanced_result["table_text"] = table_text
            enhanced_result["coordinate_based_tables"] = len(coordinate_based_tables)
            enhanced_result["text_based_tables"] = len(text_based_tables)
            enhanced_result["text"] = f"{ocr_result['text']}\n\n【復元された表構造】\n{table_text}"

            return enhanced_result

        return ocr_result

    def _reconstruct_tables_by_coordinates(self, layout_data: List[Dict]) -> List[Dict]:
        """座標情報を使った表構造復元"""
        tables = []

        # table_rowタイプのアイテムを収集
        table_rows = [item for item in layout_data if item.get("type") == "table_row"]

        if not table_rows:
            return tables

        # Y座標でグループ化（行の特定）
        row_groups = self._group_rows_by_y_coordinate(table_rows)

        # 各行グループを表として復元
        for row_group in row_groups:
            if len(row_group) >= 2:  # 最低2行以上で表とみなす
                table = self._create_table_from_row_group(row_group)
                if table:
                    tables.append(table)

        return tables

    def _group_rows_by_y_coordinate(self, table_rows: List[Dict]) -> List[List[Dict]]:
        """Y座標で行をグループ化"""
        if not table_rows:
            return []

        # Y座標でソート
        sorted_rows = sorted(table_rows, key=lambda x: self._get_y_center(x))

        groups = []
        current_group = [sorted_rows[0]]

        for i in range(1, len(sorted_rows)):
            current_y = self._get_y_center(sorted_rows[i])
            prev_y = self._get_y_center(sorted_rows[i-1])

            # Y座標の差が大きい場合は新しいグループ
            if abs(current_y - prev_y) > 0.05:  # 正規化座標で5%以上の差
                groups.append(current_group)
                current_group = [sorted_rows[i]]
            else:
                current_group.append(sorted_rows[i])

        groups.append(current_group)
        return groups

    def _get_y_center(self, item: Dict) -> float:
        """アイテムのY座標中心を取得"""
        bbox = item.get("normalized_coords", item.get("normalized_bbox", []))
        if len(bbox) >= 8:
            return (bbox[1] + bbox[5]) / 2
        return 0.0

    def _create_table_from_row_group(self, row_group: List[Dict]) -> Dict:
        """行グループから表を作成"""
        if not row_group:
            return None

        # 各行のセルをX座標でソート
        sorted_rows = []
        max_columns = 0

        for row_item in row_group:
            cells = row_item.get("cells", [])
            cell_coords = row_item.get("normalized_coords", [])

            if cells and cell_coords:
                # セルとその座標をペアにしてX座標でソート
                cell_pairs = list(zip(cells, cell_coords))
                sorted_cells = sorted(cell_pairs, key=lambda x: x[1][0] if len(x[1]) > 0 else 0)

                row_data = [cell[0] for cell in sorted_cells]
                sorted_rows.append(row_data)
                max_columns = max(max_columns, len(row_data))

        # 列数を統一
        for row in sorted_rows:
            while len(row) < max_columns:
                row.append("-")

        # 表の種類を判定
        table_type = self._identify_table_type(sorted_rows)
        table_name = self._generate_table_name(table_type, sorted_rows)
        headers = self._generate_table_headers(table_type, max_columns)

        return {
            "type": "reconstructed_table",
            "table_name": table_name,
            "headers": headers,
            "rows": sorted_rows,
            "columns": max_columns,
            "table_type": table_type,
            "reconstruction_method": "coordinate_based"
        }

    def _identify_table_type(self, rows: List[List[str]]) -> str:
        """表の種類を特定"""
        if not rows:
            return "unknown"

        # 全セルのテキストを結合して分析
        all_text = " ".join([" ".join(row) for row in rows])

        # CS表の特徴
        if re.search(r'CS|時刻.*判定|\d{1,2}:\d{2}.*\d+/\d+', all_text):
            return "cs_observation"

        # Pack表の特徴
        if re.search(r'Pack.*1st.*2nd|Pack.*mL', all_text):
            return "experimental_conditions"

        # 数値表の特徴
        if re.search(r'\d+/\d+', all_text):
            return "numerical_data"

        return "general"

    def _generate_table_name(self, table_type: str, rows: List[List[str]]) -> str:
        """表名を生成"""
        if table_type == "cs_observation":
            return "CS"
        elif table_type == "experimental_conditions":
            return "Exp"
        elif table_type == "numerical_data":
            return "Data"
        else:
            return "Table"

    def _generate_table_headers(self, table_type: str, column_count: int) -> List[str]:
        """表のヘッダーを生成"""
        if table_type == "cs_observation":
            headers = ["No."]
            pair_count = (column_count - 1) // 2
            for i in range(pair_count):
                headers.extend([f"時刻{i+1}", f"判定{i+1}"])
            while len(headers) < column_count:
                headers.append(f"列{len(headers)}")
            return headers[:column_count]

        elif table_type == "experimental_conditions":
            return ["Pack", "1st", "2nd"][:column_count]

        else:
            return [f"列{i+1}" for i in range(column_count)]

    def _deduplicate_tables(self, tables: List[Dict]) -> List[Dict]:
        """重複する表を除去"""
        if not tables:
            return []

        unique_tables = []
        seen_signatures = set()

        for table in tables:
            # 表の署名を作成（行数、列数、最初の行の内容）
            rows = table.get("rows", [])
            if rows:
                first_row = " ".join(str(cell) for cell in rows[0])
                signature = f"{len(rows)}x{table.get('columns', 0)}:{first_row[:50]}"

                if signature not in seen_signatures:
                    seen_signatures.add(signature)
                    unique_tables.append(table)

        return unique_tables

    def _looks_like_table_data(self, item: Dict) -> bool:
        """アイテムが表データかどうかを判定"""
        text = item.get("text", "")

        # 表の特徴的なパターン
        table_indicators = [
            r'\d+/\d+',           # 分数（0/5, 1/5など）
            r'\d{1,2}:\d{2}',     # 時刻（16:35など）
            r'Pack|No\.|CS|Exp',  # 表のヘッダー
            r'[─━−‐-]{3,}',      # 罫線
            r'\|\s*\w+\s*\|'      # パイプ区切り
        ]

        for pattern in table_indicators:
            if re.search(pattern, text):
                return True

        return False

    def _reconstruct_single_table(self, table_item: Dict) -> Dict:
        """単一の表を復元"""
        if table_item.get("type") == "table_row":
            # 既に表として認識されている場合
            cells = table_item.get("cells", [])
            return {
                "type": "reconstructed_table",
                "rows": [cells],
                "columns": len(cells),
                "table_type": "simple"
            }

        # テキストから表構造を推定
        text = table_item.get("text", "")

        # CS表の特別処理
        if "CS" in text or re.search(r'\d+/\d+', text):
            return self._reconstruct_cs_table(text)

        # Pack表の特別処理
        if "Pack" in text or re.search(r'\d+st|\d+nd', text):
            return self._reconstruct_pack_table(text)

        return None

    def _reconstruct_cs_table(self, text: str) -> Dict:
        """CS表（時刻・判定表）の復元"""
        lines = text.split('\n')

        # ヘッダーを検出
        header_line = None
        data_lines = []

        for line in lines:
            if re.search(r'No\.|時刻|判定', line):
                header_line = line
            elif re.search(r'\d+/\d+|\d{1,2}:\d{2}', line):
                data_lines.append(line)

        if not header_line or not data_lines:
            return None

        # ヘッダーの列を分析
        header_columns = self._parse_cs_header(header_line)

        # データ行を分析
        data_rows = []
        for line in data_lines:
            row_data = self._parse_cs_data_row(line, len(header_columns))
            if row_data:
                data_rows.append(row_data)

        return {
            "type": "reconstructed_table",
            "table_name": "CS",
            "headers": header_columns,
            "rows": data_rows,
            "columns": len(header_columns),
            "table_type": "cs_observation"
        }

    def _parse_cs_header(self, header_line: str) -> List[str]:
        """CSテーブルのヘッダーを解析"""
        # 典型的なCSヘッダーパターン
        default_headers = ["No.", "時刻1", "判定1", "時刻2", "判定2", "時刻3", "判定3"]

        # 実際のヘッダーから列数を推定
        if re.search(r'時刻.*判定', header_line):
            # 時刻・判定のペア数を数える
            time_count = len(re.findall(r'時刻', header_line))
            judgment_count = len(re.findall(r'判定', header_line))

            if time_count > 0 and judgment_count > 0:
                headers = ["No."]
                for i in range(max(time_count, judgment_count)):
                    headers.extend([f"時刻{i+1}", f"判定{i+1}"])
                return headers

        return default_headers

    def _parse_cs_data_row(self, data_line: str, expected_columns: int) -> List[str]:
        """CSテーブルのデータ行を解析"""
        # 時刻パターン（16:35）と分数パターン（0/5）を抽出
        times = re.findall(r'\d{1,2}:\d{2}', data_line)
        fractions = re.findall(r'\d+/\d+', data_line)

        # 行番号を抽出
        row_number = re.search(r'^\s*(\d+)', data_line)
        row_num = row_number.group(1) if row_number else ""

        # データを組み立て
        row_data = [row_num]

        # 時刻と判定を交互に配置
        max_pairs = (expected_columns - 1) // 2
        for i in range(max_pairs):
            time_val = times[i] if i < len(times) else "-"
            fraction_val = fractions[i] if i < len(fractions) else "-"
            row_data.extend([time_val, fraction_val])

        # 列数を調整
        while len(row_data) < expected_columns:
            row_data.append("-")

        return row_data[:expected_columns]

    def _reconstruct_pack_table(self, text: str) -> Dict:
        """Pack表（実験条件表）の復元"""
        lines = text.split('\n')

        # ヘッダーと区切り線を検出
        header_line = None
        data_lines = []

        for line in lines:
            if "Pack" in line and ("1st" in line or "2nd" in line):
                header_line = line
            elif re.search(r'^\s*\d+', line) and ("mL" in line or "DMSO" in line):
                data_lines.append(line)

        if not data_lines:
            return None

        # ヘッダーを標準化
        headers = ["Pack", "1st", "2nd"]

        # データ行を解析
        data_rows = []
        for line in data_lines:
            row_data = self._parse_pack_data_row(line)
            if row_data:
                data_rows.append(row_data)

        return {
            "type": "reconstructed_table",
            "table_name": "Exp",
            "headers": headers,
            "rows": data_rows,
            "columns": len(headers),
            "table_type": "experimental_conditions"
        }

    def _parse_pack_data_row(self, data_line: str) -> List[str]:
        """Pack表のデータ行を解析"""
        # Pack番号を抽出
        pack_num = re.search(r'^\s*(\d+)', data_line)
        pack_number = pack_num.group(1) if pack_num else ""

        # 条件を分離（mLやDMSOなどのキーワードで分割）
        conditions = []

        # 1st条件（最初のmLまで）
        first_condition = re.search(r'(\w+\s+\d+\s*mL)', data_line)
        if first_condition:
            conditions.append(first_condition.group(1))

        # 2nd条件（残りの部分）
        remaining = data_line
        if first_condition:
            remaining = data_line[first_condition.end():]

        second_condition = re.search(r'(\w+.*mL|\w+.*DMSO.*)', remaining)
        if second_condition:
            conditions.append(second_condition.group(1).strip())

        # データを組み立て
        row_data = [pack_number]
        row_data.extend(conditions)

        # 3列に調整
        while len(row_data) < 3:
            row_data.append("-")

        return row_data[:3]

    def _format_reconstructed_tables(self, tables: List[Dict]) -> str:
        """復元された表をフォーマット"""
        formatted_text = []

        for table in tables:
            table_name = table.get("table_name", "Table")
            headers = table.get("headers", [])
            rows = table.get("rows", [])

            # 表のタイトル
            formatted_text.append(f"\n{table_name}")
            formatted_text.append("─" * 50)

            # ヘッダー
            if headers:
                header_row = " | ".join(f"{h:^10}" for h in headers)
                formatted_text.append(header_row)
                formatted_text.append("─" * len(header_row))

            # データ行
            for row in rows:
                data_row = " | ".join(f"{cell:^10}" for cell in row)
                formatted_text.append(data_row)

            formatted_text.append("─" * 50)

        return "\n".join(formatted_text)





    def _detect_languages(self, text: str) -> List[str]:
        """
        テキストに含まれる言語を検出
        """
        languages = []
        
        if self._contains_japanese(text):
            languages.append("ja")
        if self._contains_english(text):
            languages.append("en")
        if self._contains_chinese(text):
            languages.append("zh")
        
        return languages if languages else ["unknown"]

    def _contains_japanese(self, text: str) -> bool:
        """日本語文字が含まれているかチェック"""
        import re
        return bool(re.search(r'[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]', text))

    def _contains_english(self, text: str) -> bool:
        """英語文字が含まれているかチェック"""
        import re
        return bool(re.search(r'[a-zA-Z]', text))

    def _contains_chinese(self, text: str) -> bool:
        """中国語文字が含まれているかチェック"""
        import re
        return bool(re.search(r'[\u4E00-\u9FFF]', text))

    def get_available_methods(self) -> List[str]:
        """利用可能なOCR方法を返す"""
        methods = []
        if self.azure_available:
            methods.extend(["azure", "azure_structured", "azure_layout"])
        return methods

    def extract_text(self, image_data: bytes, method: str = "auto", output_format: str = "layout") -> Dict[str, Any]:
        """
        Azure Computer Vision APIでOCR処理を実行
        """
        if not self.azure_available:
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "method": "none",
                "error": "Azure Computer Vision API is not available"
            }

        if method in ["auto", "azure", "azure_layout"]:
            return self.extract_text_azure(image_data, output_format)
        else:
            return {
                "success": False,
                "text": "",
                "confidence": 0,
                "method": method,
                "error": f"Unsupported OCR method: {method}. Only Azure is supported."
            }


# グローバルインスタンス
ocr_service = OCRService()
