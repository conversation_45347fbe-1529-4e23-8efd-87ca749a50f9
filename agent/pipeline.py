from __future__ import annotations
import os, re, csv, json, uuid, tempfile, xml.etree.ElementTree as ET
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple, Any, Optional
import openai
from Bio import Entrez
from langchain_openai import ChatOpenAI
from agent.token_meter import METER   # OpenAI 使用量メータ
from queue import Queue, Empty
import logging, textwrap

DEBUG_PROGRESS = os.getenv("DEBUG_PROGRESS", "0") == "1"
logging.basicConfig(
    level=logging.DEBUG if DEBUG_PROGRESS else logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s"
)

# ───── 0. 初期化 ────────────────────────────────────
Entrez.email = "<EMAIL>"
Entrez.api_key = os.getenv("NCBI_API_KEY") 

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set")
openai_api_client = openai.OpenAI(api_key=OPENAI_API_KEY)
llm_small = ChatOpenAI(openai_api_key=OPENAI_API_KEY, model_name="gpt-4.1-mini")

# ───── 1. 定数／ユーティリティ ──────────────────────
DETAIL_HEADERS = [
    "sent_idx", "phase", "sentence",
    "keyword", "query",
    "pmid", "title",
    "score", "reason",
    "accepted",
    "explanation"
]

def push(progress, obj):
    """progress があれば JSON 文字列化して渡す"""
    if progress:
        progress(json.dumps(obj, ensure_ascii=False))

def split_sentences(text):
    """
    テキストを文単位に分割する関数（日本語対応版）
    段落構造を保持し、著者業績の便宜的番号を除去
    """
    # 段落区切りを保護
    text = re.sub(r'\n\s*\n', '__PARAGRAPH_BREAK__', text)

    # 著者業績の便宜的番号を除去（論文1、論文5など）
    # より柔軟なパターンで複数の論文番号に対応
    text = re.sub(r'（論文\d+(?:[、，]\s*論文\d+)*）', '', text)
    text = re.sub(r'\(論文\d+(?:[、，]\s*論文\d+)*\)', '', text)  # 半角括弧も対応

    # 英語の文分割ロジック
    english_sentences = []
    current = ""

    for i, char in enumerate(text):
        current += char

        # 英語の文末判定
        if char in ['.', '!', '?'] and i + 1 < len(text) and text[i + 1] in [' ', '\n', '\t']:
            # 小数点や略語の処理
            if (char == '.' and i > 0 and text[i-1].isdigit() and
                (i + 1 < len(text) and text[i+1].isdigit())):
                continue

            if (char == '.' and i > 1 and
                ((text[i-2:i] in ['Dr', 'Mr', 'Ms', 'Jr'] or
                 text[i-3:i] in ['Mrs', 'Rev', 'Sen', 'St.', 'vs.', 'etc', 'Fig'] or
                 text[i-4:i] in ['Prof', 'Capt', 'e.g.', 'i.e.']))):
                continue

            english_sentences.append(current.strip())
            current = ""

    # 最後の文が句点で終わっていない場合も追加
    if current.strip():
        english_sentences.append(current.strip())

    # 日本語の文分割ロジック
    japanese_sentences = []
    for sentence in english_sentences:
        # 日本語の文末「。」「！」「？」で分割
        temp = ""
        for char in sentence:
            temp += char
            if char in ["。", "！", "？"]:
                japanese_sentences.append(temp.strip())
                temp = ""

        # 残りの部分があれば追加
        if temp.strip():
            japanese_sentences.append(temp.strip())

    # 段落区切りを復元
    final_sentences = []
    for sentence in japanese_sentences:
        sentence = sentence.replace('__PARAGRAPH_BREAK__', '\n\n')
        if sentence.strip():
            final_sentences.append(sentence)

    # 日本語文が検出された場合は日本語分割を返し、そうでなければ英語分割を返す
    has_japanese = any("。" in s or "、" in s for s in english_sentences)
    return final_sentences if has_japanese else english_sentences

def identify_target_sentences(text: str, sentences: List[str], enable_advanced_features: bool = True) -> List[int]:
    """
    対象文のインデックスを特定する

    Args:
        text: 元のテキスト
        sentences: 分割された文のリスト
        enable_advanced_features: Writer用の高度な機能を有効にするかどうか

    Returns:
        List[int]: 対象文のインデックスリスト
    """
    if not enable_advanced_features:
        # Citer2用: 全ての文を対象にする
        from agent.citer2_features import identify_all_sentences
        return identify_all_sentences(text, sentences)

    # Writer用: イントロダクションと考察セクションのみを対象にする
    import re

    target_indices = []

    # セクション境界を特定
    intro_patterns = [
        r'(?:^|\n)(イントロダクション|はじめに|序論)\s*\n',
        r'(?:^|\n)(Introduction)\s*\n'
    ]

    discussion_patterns = [
        r'(?:^|\n)(考察|ディスカッション)\s*\n',
        r'(?:^|\n)(Discussion)\s*\n'
    ]

    # 除外セクションのパターン
    exclude_patterns = [
        r'(?:^|\n)(材料と方法|方法|実験方法|結果|結論|謝辞|参考文献|文献リスト|文献|図の説明|表の説明)\s*\n',
        r'(?:^|\n)(Materials and Methods|Methods|Results|Conclusion|Acknowledgments|References|Figure|Table)\s*\n'
    ]

    # 各セクションの開始位置を特定
    sections = []

    # イントロダクション
    for pattern in intro_patterns:
        matches = list(re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE))
        for match in matches:
            sections.append({
                'name': 'intro',
                'start': match.end(),
                'type': 'target'
            })

    # 考察
    for pattern in discussion_patterns:
        matches = list(re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE))
        for match in matches:
            sections.append({
                'name': 'discussion',
                'start': match.end(),
                'type': 'target'
            })

    # 除外セクション
    for pattern in exclude_patterns:
        matches = list(re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE))
        for match in matches:
            sections.append({
                'name': 'exclude',
                'start': match.start(),
                'type': 'exclude'
            })

    # 位置順にソート
    sections.sort(key=lambda x: x['start'])

    # 各文がどのセクションに属するかを判定
    current_pos = 0
    current_section_type = None

    for i, sentence in enumerate(sentences):
        # 文の位置を特定
        sentence_pos = text.find(sentence, current_pos)
        if sentence_pos == -1:
            continue

        # この文の位置でセクションが変わるかチェック
        for section in sections:
            if section['start'] <= sentence_pos:
                current_section_type = section['type']

        # 対象セクションの文のみを追加
        if current_section_type == 'target':
            target_indices.append(i)

        current_pos = sentence_pos + len(sentence)

    return target_indices

def endnote_tag(p):
    # authorsフィールドから最初の著者を取得
    authors = p.get("authors", [])
    if isinstance(authors, list) and authors:
        first = authors[0].split(",")[0].strip()
    elif isinstance(authors, str) and authors:
        first = authors.split(";")[0].split(",")[0].strip()
    else:
        first = "Anon"
    return f"{{{first}, {p.get('year', 'XXXX')} #{p.get('pmid', 'XXXX')}}}"

def to_ris(p, n):
    # 著者情報を安全に処理
    authors = p.get("authors", [])
    author_lines = []
    if isinstance(authors, list):
        author_lines = [f"AU  - {au.strip()}" for au in authors if au.strip()]
    elif isinstance(authors, str) and authors:
        author_lines = [f"AU  - {au.strip()}" for au in authors.split(";") if au.strip()]

    ris = ["TY  - JOUR", f"TI  - {p.get('title', '')}"] + author_lines

    if p.get("journal"):  ris.append(f"JF  - {p['journal']}")
    if p.get("year"):     ris.append(f"PY  - {p['year']}")
    if p.get("volume"):   ris.append(f"VL  - {p['volume']}")
    if p.get("issue"):    ris.append(f"IS  - {p['issue']}")
    if p.get("pages"):    ris.append(f"SP  - {p['pages']}")
    if p.get("doi"):      ris.append(f"DO  - {p['doi']}")

    # URLとabstractも安全にアクセス
    if p.get("url"):      ris.append(f"UR  - {p['url']}")
    if p.get("abstract"): ris.append(f"AB  - {p['abstract']}")
    if p.get("pmid"):     ris.append(f"ID  - {p['pmid']}")

    ris.append("ER  - ")
    return "\n".join(ris) + "\n"

# ───── 2. PubMed / PMC ─────────────────────────────
def get_pmcid_from_pmid(pmid) -> str | None:
    try:
        rec = Entrez.read(Entrez.elink(dbfrom="pubmed", db="pmc", id=pmid))
        for db in rec[0].get("LinkSetDb", []):
            if db.get("DbTo") == "pmc":
                for link in db.get("Link", []):
                    return link.get("Id")
    except Exception:
        pass
    return None

def get_pmc_fulltext(pmcid) -> str | None:
    try:
        xml = Entrez.efetch(db="pmc", id=pmcid, rettype="full", retmode="xml").read()
        root = ET.fromstring(xml)
        return "\n".join(
            "".join(p.itertext()).strip()
            for p in root.findall(".//body//p")
            if "".join(p.itertext()).strip())
    except Exception:
        return None

# ───── 3. GPT 呼び出しヘルパー ─────────────────────
def add_usage(model, rsp):
    METER.add(model, rsp.usage.prompt_tokens, rsp.usage.completion_tokens)

def extract_keywords(text: str, model: str = "gpt-4.1-mini") -> List[str]:
    """
    テキストから重要なキーワードを抽出する（英語）
    """
    system_prompt = """
    You are an expert in scientific literature keyword extraction.
    Extract up to 5 important keywords from the given text that are suitable for PubMed searches.

    IMPORTANT: All keywords must be in English, even if the input text is in Japanese or other languages.
    Keywords should be single words or short phrases that are effective for scientific literature searches.
    Focus on biological, medical, pharmaceutical, and scientific terms.

    Output only a JSON array format. Example: ["cytokine storm", "inflammation", "drug screening", "traditional medicine", "bioactive compounds"]
    """
    
    user_prompt = f"""
    Extract English keywords from the following text for PubMed search:

    Text: {text}

    Remember: Output keywords must be in English regardless of the input language.
    """

    response = openai.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        response_format={"type": "json_object"}
    )
    
    try:
        result = json.loads(response.choices[0].message.content)
        return result.get("keywords", []) if isinstance(result, dict) else result
    except json.JSONDecodeError:
        # JSON解析に失敗した場合、テキストから直接抽出を試みる
        content = response.choices[0].message.content
        match = re.search(r'\[.*\]', content)
        if match:
            try:
                return json.loads(match.group(0))
            except:
                pass
        # それでも失敗したら空リストを返す
        return []

def build_queries(keywords: List[str]) -> List[str]:
    """
    キーワードからPubMed検索クエリを生成する
    """
    queries = []
    
    # 2つのキーワードの組み合わせ
    if len(keywords) >= 2:
        for i in range(len(keywords)):
            for j in range(i+1, len(keywords)):
                queries.append(f'"{keywords[i]}"[Title/Abstract] AND "{keywords[j]}"[Title/Abstract]')
    
    # 3つのキーワードの組み合わせ（上位3つのキーワードのみ）
    if len(keywords) >= 3:
        top_keywords = keywords[:3]
        queries.append(' AND '.join([f'"{kw}"[Title/Abstract]' for kw in top_keywords]))
    
    return queries

# セクション重複除去機能は agent/writer_features.py に移動

# Writer専用機能は agent/writer_features.py に移動

# 包括的なテキスト整理機能は各アプリケーション専用ファイルに移動

# 文中引用機能は agent/writer_features.py に移動

def clean_duplicate_citations(text: str, model: str = "gpt-4.1-mini") -> str:
    """
    GPTを使って重複した引用番号を除去する
    """
    import openai
    import re

    # 重複引用パターンを検出
    duplicate_pattern = r'\[([0-9,\s]+)\]'
    matches = re.findall(duplicate_pattern, text)

    if not matches:
        return text

    # 重複がありそうな引用を特定
    problematic_citations = []
    for match in matches:
        numbers = [int(n.strip()) for n in match.split(',') if n.strip().isdigit()]
        if len(numbers) != len(set(numbers)):  # 重複がある場合
            problematic_citations.append(match)

    if not problematic_citations:
        return text

    system_prompt = """
    あなたは学術論文の引用番号整理の専門家です。
    重複した引用番号を除去し、適切な形式に修正してください。

    ルール:
    1. 同じ番号の重複は除去（例: [7,8,7] → [7,8]）
    2. 番号は昇順に並べる（例: [8,7] → [7,8]）
    3. 連続番号は範囲表記にしない（[1,2,3] → [1,2,3] のまま）

    元のテキストの内容は変更せず、引用番号のみを修正してください。
    """

    user_prompt = f"以下のテキストの重複引用番号を修正してください:\n\n{text}"

    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
        )

        return response.choices[0].message.content.strip()

    except Exception:
        # エラー時は正規表現で基本的な重複除去
        def fix_citation(match):
            numbers = match.group(1)
            unique_numbers = []
            for n in numbers.split(','):
                n = n.strip()
                if n.isdigit() and n not in unique_numbers:
                    unique_numbers.append(n)
            return f"[{','.join(sorted(unique_numbers, key=int))}]"

        return re.sub(duplicate_pattern, fix_citation, text)

# 図表説明判定と文分類機能は agent/writer_features.py に移動

def ai_is_citable_scored(sentence: str, paper_info, model: str = "gpt-4.1-mini"):
    """
    文と論文の関連性をAIでスコアリングする
    既存のpipeline.pyの構造に完全対応
    """
    import openai
    import json
    
    # paper_infoを統一的に処理
    if isinstance(paper_info, str):
        # 文字列（PMID）の場合
        pmid = paper_info
        paper_text = f"PMID: {pmid}"

    elif isinstance(paper_info, dict):
        # 辞書の場合
        pmid = paper_info.get("pmid", "unknown")

        # 論文情報を文字列に整形
        title = paper_info.get("title", "N/A")
        authors = paper_info.get("authors", [])
        if isinstance(authors, list):
            authors_str = ", ".join(authors)
        else:
            authors_str = str(authors)

        journal = paper_info.get("journal", "N/A")
        year = paper_info.get("year", "N/A")
        abstract = paper_info.get("abstract", "N/A")

        paper_text = f"""
        タイトル: {title}
        著者: {authors_str}
        ジャーナル: {journal} ({year})
        要約: {abstract}
        """

    else:
        # その他の型の場合はエラー
        return {
            "score": 0,
            "reason": f"不正なpaper_info型: {type(paper_info)}",
            "pmid": str(paper_info) if paper_info else "unknown"
        }

    # 統一されたプロンプト（論文情報の詳細度に関わらず同じ評価基準）
    system_prompt = """
    あなたは科学論文の引用評価の専門家です。
    与えられた文と論文の情報を比較し、この論文がこの文の引用として適切かどうかを評価してください。

    ## 文の種類別判定基準

    ### A. 引用が不適切な文（スコア1-2）
    **明確な独自主張・新規発見を示す表現:**
    - 「本研究では」「我々は」「著者らは」で始まる文
    - 「初めて」「新たに」「独自に」「提案する」「開発した」
    - 数値結果の報告（「〜%であった」「有意差が認められた」）

    **注意: 「accomplished」「achieved」「discovered」などの表現は、文脈により背景知識として適切な場合があります**

    **将来展望・課題を示す表現:**
    - 「今後」「将来」「課題として」「検討が必要」
    - 「期待される」「可能性がある」「と考えられる」

    ### B. 引用が必要な文（スコア4-5）
    **既知事実・背景知識を示す表現:**
    - 「〜は知られている」「〜が報告されている」
    - 「従来」「一般的に」「これまで」
    - 疾患・現象の定義や説明
    - 既存手法・技術の説明

    **先行研究への言及:**
    - 「先行研究では」「過去の報告で」
    - 「〜によると」「〜において」

    ### C. 条件付き引用（スコア3-4）
    **関連性に基づく判定:**
    - 研究対象が同じ（同じ生物種、化合物、疾患など）
    - 研究手法が類似（同じ実験系、評価方法など）
    - 研究分野が関連（同じ学問領域、応用分野など）

    ## スコアリング基準
    - **スコア5**: 文が既知事実を述べ、論文が直接的な根拠となる
    - **スコア4**: 文が背景知識を述べ、論文が関連する証拠を提供
    - **スコア3**: 文と論文に間接的関連性があるが、直接的根拠ではない
    - **スコア2**: 文が独自主張を含むか、論文との関連性が低い
    - **スコア1**: 文が明確な独自主張であるか、論文との関連性がない

    注意: 論文情報が限定的（PMIDのみ）の場合は、文の表現パターンを重視して判定してください。

    出力は以下のJSON形式のみにしてください:
    {"score": 数値, "reason": "判定理由（該当する表現と分類根拠を明記）"}
    """

    user_prompt = f"文: {sentence}\n\n論文情報:\n{paper_text}"
    
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            response_format={"type": "json_object"}
        )
        
        result = json.loads(response.choices[0].message.content)
        
        return {
            "score": result.get("score", 0),
            "reason": result.get("reason", ""),
            "pmid": pmid
        }
        
    except Exception as e:
        return {
            "score": 0,
            "reason": f"評価エラー: {str(e)}",
            "pmid": pmid
        }

def ai_parallel(papers, sent, progress=None):
    """
    並列でAI評価を実行する関数
    既存のコードの構造を維持しつつ、型エラーを修正
    """
    import concurrent.futures
    import logging
    
    # papers が文字列のリストか辞書のリストかを判定
    processed_papers = []
    
    for p in papers:
        if isinstance(p, str):
            # 文字列（PMID）の場合、辞書形式に変換
            processed_papers.append({"pmid": p})
        elif isinstance(p, dict):
            # 既に辞書の場合はそのまま使用
            processed_papers.append(p)
        else:
            # その他の型の場合はスキップ
            logging.warning("Unexpected paper type: %s", type(p))
            continue
    
    # 並列処理の実行
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        # 各論文に対してAI評価を並列実行
        future_to_paper = {}
        
        for paper in processed_papers:
            future = executor.submit(ai_is_citable_scored, sent, paper)
            future_to_paper[future] = paper
        
        # 結果を収集
        results = []
        for future in concurrent.futures.as_completed(future_to_paper):
            paper = future_to_paper[future]
            try:
                # AI評価の結果を取得
                result = future.result()
                
                # 結果を論文情報に追加
                print(f"DEBUG: AI evaluation result type: {type(result)}, content: {result}")
                if isinstance(paper, dict):
                    paper_copy = paper.copy()
                    # AI評価結果から必要な情報を抽出
                    if isinstance(result, dict):
                        paper_copy["score"] = result.get("score", 0)
                        paper_copy["citable_decision"] = result.get("reason", "")
                        print(f"DEBUG: Extracted score={result.get('score', 0)} from dict result")
                    else:
                        paper_copy["score"] = 0
                        paper_copy["citable_decision"] = str(result)
                        print(f"DEBUG: Non-dict result, setting score=0")
                    results.append(paper_copy)
                else:
                    # 文字列の場合の処理
                    if isinstance(result, dict):
                        results.append({
                            "pmid": str(paper),
                            "score": result.get("score", 0),
                            "citable_decision": result.get("reason", "")
                        })
                    else:
                        results.append({
                            "pmid": str(paper),
                            "score": 0,
                            "citable_decision": str(result)
                        })
                    
            except Exception as e:
                # エラーハンドリング - 型チェックを追加
                if isinstance(paper, dict):
                    pmid = paper.get("pmid", "unknown")
                elif isinstance(paper, str):
                    pmid = paper
                else:
                    pmid = str(paper)
                    
                logging.warning("ai_scoring failed for PMID %s : %s", pmid, str(e))
                
                # エラーが発生した場合もリストに追加（スコア0で）
                error_result = {
                    "pmid": pmid,
                    "score": 0,
                    "citable_decision": f"評価エラー: {str(e)}"
                }
                results.append(error_result)
    
    return results

# ───── 4. PubMed 検索 ─────────────────────────────
def get_paper_details(pmid: str) -> Dict[str, Any]:
    """
    PMIDから論文の詳細情報を取得する
    """
    try:
        # 論文情報を取得
        fetch_handle = Entrez.efetch(db="pubmed", id=pmid, retmode="xml")
        records = Entrez.read(fetch_handle)
        fetch_handle.close()
        
        if not records["PubmedArticle"]:
            return {"pmid": pmid, "error": "No article found"}
        
        article = records["PubmedArticle"][0]["MedlineCitation"]["Article"]
        
        # 著者情報
        authors = []
        if "AuthorList" in article:
            for author in article["AuthorList"]:
                if "LastName" in author and "ForeName" in author:
                    authors.append(f"{author['LastName']} {author['ForeName']}")
                elif "LastName" in author:
                    authors.append(author["LastName"])
                elif "CollectiveName" in author:
                    authors.append(author["CollectiveName"])
        
        # 要約
        abstract = ""
        if "Abstract" in article:
            abstract_parts = article["Abstract"].get("AbstractText", [])
            if isinstance(abstract_parts, list):
                abstract = " ".join([str(part) for part in abstract_parts])
            else:
                abstract = str(abstract_parts)
        
        # 結果を辞書にまとめる
        return {
            "pmid": pmid,
            "title": article.get("ArticleTitle", ""),
            "authors": authors,  # リスト形式
            "author": "; ".join(authors) if authors else "",  # 文字列形式（互換性のため）
            "journal": article.get("Journal", {}).get("Title", ""),
            "year": article.get("Journal", {}).get("JournalIssue", {}).get("PubDate", {}).get("Year", ""),
            "abstract": abstract,
            "url": f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/"  # PubMed URL
        }
    except Exception as e:
        print(f"DEBUG: Error getting paper details for PMID {pmid}: {e}")
        return {"pmid": pmid, "error": str(e)}

def enhanced_citation_pipeline(sent, query, hits, progress=None, enable_advanced_features=True):
    """
    文の分類を考慮した改善された引用パイプライン
    enable_advanced_features: Writer用の高度な機能を有効にするかどうか
    """
    if not enable_advanced_features:
        # Citer2用: 基本的な引用処理のみ
        return find_citable_references(sent, query, hits, progress)

    # Writer用: 高度な分類機能を適用
    try:
        from agent.writer_features import is_figure_table_description, classify_sentence_type

        # 図表説明かどうかをチェック
        if is_figure_table_description(sent):
            push(progress, {"step": "classification", "msg": "図表説明のため引用スキップ"})
            return []

        # 文の種類を分類
        sentence_classification = classify_sentence_type(sent)

        # 引用が不要な文の場合は早期リターン
        if sentence_classification.get("citation_need") == "not_needed":
            push(progress, {"step": "classification", "msg": f"引用不要: {sentence_classification.get('reason', '')}"})
            return []

        # 引用が必要な場合は通常の処理を実行
        push(progress, {"step": "classification", "msg": f"引用処理: {sentence_classification.get('sentence_type', '')}"})
        return find_citable_references(sent, query, hits, progress)

    except ImportError:
        # Writer機能が利用できない場合は基本処理
        return find_citable_references(sent, query, hits, progress)

def find_citable_references(sent, query, hits, progress=None):
    print(f"DEBUG: Searching PubMed with query: {query}")
    pmids = Entrez.read(
        Entrez.esearch(db="pubmed", term=query, retmax=hits)
    ).get("IdList", [])
    push(progress, {"step":"pubmed","msg":f"PubMed {len(pmids)} hits"})

    # 複数のPMIDを並列処理
    papers = []
    if pmids:
        import concurrent.futures

        def get_paper_with_delay(pmid):
            """APIレート制限を考慮してPMIDから論文詳細を取得"""
            # BioPythonが既に0.1秒間隔で制御しているので追加のsleepは不要
            return get_paper_details(pmid)

        # 並列でPMIDから論文詳細を取得（max_workers=10で並列）
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_pmid = {executor.submit(get_paper_with_delay, pmid): pmid for pmid in pmids}

            try:
                for future in concurrent.futures.as_completed(future_to_pmid, timeout=60):  # 60秒でタイムアウト
                    pmid = future_to_pmid[future]
                    try:
                        paper = future.result(timeout=30)  # 個別タスクは30秒でタイムアウト
                        if "error" not in paper:  # エラーがない場合のみ追加
                            papers.append(paper)
                    except concurrent.futures.TimeoutError:
                        print(f"DEBUG: Timeout getting details for PMID {pmid}")
                    except Exception as e:
                        print(f"DEBUG: Error getting details for PMID {pmid}: {e}")
            except concurrent.futures.TimeoutError:
                print(f"DEBUG: Overall timeout in parallel paper processing")

            # 並列処理完了後にfuture辞書をクリア
            future_to_pmid.clear()
            del future_to_pmid

    print(f"DEBUG: Retrieved {len(papers)} papers from {len(pmids)} PMIDs for query: {query}")

    # ai_parallel実行前にPMIDリストをクリア（メモリ節約）
    pmids.clear()
    del pmids

    result = ai_parallel(papers, sent, progress)

    # 処理完了後にpapersリストもクリア
    papers.clear()
    del papers

    return result

def search_author_publications(author_last_name: str, author_first_name: str, years_back: int = 25) -> List[Dict]:
    """
    著者名で過去の論文を検索する

    Args:
        author_last_name: 著者の姓
        author_first_name: 著者の名
        years_back: 検索対象年数（デフォルト25年）

    Returns:
        List[Dict]: 著者の論文リスト
    """
    try:
        from datetime import datetime
        current_year = datetime.now().year
        start_year = current_year - years_back

        # 著者名クエリを構築（複数の形式を試す）
        author_queries = [
            f'"{author_last_name} {author_first_name[0]}"[Author]',  # "Yamada T"
            f'"{author_last_name}, {author_first_name[0]}"[Author]',  # "Yamada, T"
            f'"{author_first_name} {author_last_name}"[Author]',      # "Taro Yamada"
        ]

        all_papers = []
        seen_pmids = set()

        for query in author_queries:
            # 年数制限を追加
            date_query = f"{query} AND {start_year}:{current_year}[PDAT]"
            print(f"DEBUG: Searching author publications with query: {date_query}")

            try:
                pmids = Entrez.read(
                    Entrez.esearch(db="pubmed", term=date_query, retmax=50)  # 200→50に制限
                ).get("IdList", [])
                print(f"DEBUG: Found {len(pmids)} papers for author query: {date_query}")

                # PMIDを並列処理で取得（重複チェックを事前に実行）
                if pmids:
                    import concurrent.futures

                    # 重複を事前に除去（thread-safe）
                    unique_pmids = [pmid for pmid in pmids if pmid not in seen_pmids]
                    seen_pmids.update(unique_pmids)

                    def get_author_paper_with_delay(pmid):
                        """APIレート制限を考慮して著者論文詳細を取得"""
                        try:
                            paper_info = get_paper_details(pmid)
                            if "error" not in paper_info:
                                # 著者名が実際に含まれているかチェック
                                authors = paper_info.get('authors', [])
                                if isinstance(authors, list):
                                    author_match = any(
                                        author_last_name.lower() in author.lower() and
                                        author_first_name[0].lower() in author.lower()
                                        for author in authors
                                    )
                                else:
                                    author_match = (author_last_name.lower() in str(authors).lower() and
                                                  author_first_name[0].lower() in str(authors).lower())

                                if author_match:
                                    return paper_info
                        except Exception as e:
                            print(f"DEBUG: Failed to get info for author PMID {pmid}: {e}")
                        return None

                    # 並列で著者論文詳細を取得（max_workers=2でサーバー負荷軽減）
                    if unique_pmids:
                        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                            future_to_pmid = {executor.submit(get_author_paper_with_delay, pmid): pmid for pmid in unique_pmids}

                            try:
                                for future in concurrent.futures.as_completed(future_to_pmid, timeout=120):  # 2分でタイムアウト
                                    try:
                                        paper_info = future.result(timeout=30)  # 個別タスクは30秒でタイムアウト
                                        if paper_info:
                                            all_papers.append(paper_info)
                                    except concurrent.futures.TimeoutError:
                                        print(f"DEBUG: Timeout in author paper processing")
                                    except Exception as e:
                                        print(f"DEBUG: Error in parallel author paper processing: {e}")
                            except concurrent.futures.TimeoutError:
                                print(f"DEBUG: Overall timeout in author paper processing")

            except Exception as e:
                print(f"DEBUG: Error in author query {query}: {e}")
                continue

        # 年度順にソート（新しい順）
        all_papers.sort(key=lambda x: int(x.get('year', 0)), reverse=True)

        # メモリ使用量制限：最大100件まで
        if len(all_papers) > 100:
            all_papers = all_papers[:100]
            print(f"DEBUG: Limited author publications to 100 (was {len(all_papers)})")

        print(f"DEBUG: Total author publications found: {len(all_papers)}")

        # メモリ解放: 一時変数をクリア
        seen_pmids.clear()
        del seen_pmids

        return all_papers

    except Exception as e:
        print(f"DEBUG: Error in search_author_publications: {e}")
        return []
    finally:
        # 例外時もメモリ解放
        import gc
        gc.collect()  # ガベージコレクション実行

def is_valid_pmid(pmid: str) -> bool:
    """
    PMIDが有効な形式かをチェックする

    Args:
        pmid: チェックするPMID

    Returns:
        bool: 有効な場合True
    """
    if not pmid or pmid == 'N/A':
        return False

    # PMIDは数字のみで構成される
    try:
        int(pmid)
        return len(pmid) >= 1 and len(pmid) <= 10  # 一般的なPMIDの長さ
    except ValueError:
        return False

def analyze_author_research_history(author_papers: List[Dict], current_research_text: str, model: str = "gpt-4o-mini") -> Dict:
    """
    著者の過去の研究を分析し、現在の研究との繋がりを理解する

    Args:
        author_papers: 著者の過去の論文リスト
        current_research_text: 現在の研究内容
        model: 使用するGPTモデル

    Returns:
        Dict: 分析結果と推奨引用
    """
    if not author_papers:
        return {"analysis": "", "recommended_citations": [], "intro_sentences": []}

    # 論文情報を要約（PMIDは含めるが、GPTには論文番号のみを使わせる）
    papers_summary = []
    paper_index_to_pmid = {}  # 論文番号からPMIDへのマッピング

    for idx, paper in enumerate(author_papers[:50]):  # 最大50件まで分析
        paper_num = idx + 1
        summary = f"論文{paper_num}: Year: {paper.get('year', 'N/A')}, Title: {paper.get('title', 'N/A')}"
        if paper.get('abstract'):
            summary += f", Abstract: {paper.get('abstract', '')[:200]}..."
        papers_summary.append(summary)
        paper_index_to_pmid[paper_num] = paper.get('pmid', 'N/A')

    system_prompt = """
    あなたは科学論文執筆の専門家です。著者の過去の研究論文と現在の研究内容を分析し、
    学術的に自然で説得力のあるイントロダクション文章を生成してください。

    重要な制約：
    - 論文を参照する際は、必ず「論文1」「論文2」などの番号のみを使用してください
    - PMIDや論文IDを生成したり推測したりしないでください
    - 提供された論文リストにない論文は参照しないでください

    タスク：
    1. 著者の過去の研究を理解し、現在の研究との繋がりを分析
    2. 現在の論文のイントロダクションの文脈に自然に溶け込む文章を生成
    3. 著者の研究歴史が現在の研究にどう発展したかを示す
    4. 学術論文として適切な文体と論理的な流れを維持

    文章生成の指針：
    - 現在の研究内容と論文の文脈を十分に理解する
    - 著者の過去の研究が現在の研究にどう繋がるかを明確に示す
    - 自然で読みやすい文章にする（テンプレート的な表現を避ける）
    - 各文に適切な論文番号を割り当てる
    - 2-4文程度で簡潔にまとめる

    出力は以下のJSON形式にしてください：
    {
        "research_evolution": "著者の研究の変遷と現在の研究への発展の説明",
        "connection_analysis": "現在の研究と過去の研究の具体的な繋がりの分析",
        "intro_sentences": [
            {
                "sentence": "現在の論文の文脈に合わせた自然な文章",
                "paper_numbers": [関連する論文番号のリスト],
                "rationale": "この文章を生成した理由と論文との関連性"
            }
        ],
        "recommended_citations": [
            {
                "paper_number": 論文番号,
                "relevance_reason": "現在の研究との関連性の詳細な理由"
            }
        ]
    }
    """

    # 現在の研究のイントロダクション部分を抽出
    intro_section = ""
    lines = current_research_text.split('\n')
    in_intro = False
    for line in lines:
        if any(keyword in line for keyword in ["イントロダクション", "はじめに", "序論", "Introduction"]):
            in_intro = True
            intro_section += line + '\n'
        elif in_intro and any(keyword in line for keyword in ["材料と方法", "方法", "結果", "考察", "Methods", "Results", "Discussion"]):
            break
        elif in_intro:
            intro_section += line + '\n'

    user_prompt = f"""
    【現在の研究の全体内容】
    {current_research_text[:2000]}...

    【現在の研究のイントロダクション部分】
    {intro_section}

    【著者の過去の論文リスト】
    {chr(10).join(papers_summary)}

    【分析タスク】
    上記の情報を基に、以下を実行してください：

    1. 現在の研究の内容、目的、アプローチを理解する
    2. 著者の過去の研究から、現在の研究に関連するものを特定する
    3. 著者の研究がどのように現在の研究に発展したかの流れを理解する
    4. 現在のイントロダクションの文脈に自然に溶け込む文章を生成する

    生成する文章は：
    - 現在の論文の読者が理解しやすい文脈で書く
    - 著者の過去の研究が現在の研究の基盤となっていることを示す
    - 学術論文として自然で説得力のある表現を使う
    - 現在のイントロダクションの論理的な流れを損なわない
    """

    try:
        import openai
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            response_format={"type": "json_object"}
        )

        result = json.loads(response.choices[0].message.content)

        # 論文番号を実際のPMIDに変換し、論文情報を追加
        # GPTが生成した便宜的な論文番号（論文1、論文5など）を除去
        validated_citations = []

        for citation in result.get('recommended_citations', []):
            paper_number = citation.get('paper_number')
            if paper_number and paper_number in paper_index_to_pmid:
                pmid = paper_index_to_pmid[paper_number]

                # 対応する論文情報を取得
                paper = None
                for p in author_papers:
                    if p.get('pmid') == pmid:
                        paper = p
                        break

                if paper and is_valid_pmid(pmid):
                    validated_citation = {
                        'pmid': pmid,
                        'title': paper.get('title', ''),
                        'year': paper.get('year', ''),
                        'authors': paper.get('authors', []),
                        'journal': paper.get('journal', ''),
                        'abstract': paper.get('abstract', ''),
                        'relevance_reason': citation.get('relevance_reason', '')
                    }
                    validated_citations.append(validated_citation)
                    print(f"DEBUG: Validated citation for paper {paper_number} -> PMID {pmid}")
                else:
                    print(f"DEBUG: Paper not found or invalid PMID for paper number {paper_number}: {pmid}")
            else:
                print(f"DEBUG: Invalid paper number: {paper_number}")

        # intro_sentences の論文番号もPMIDに変換
        validated_intro_sentences = []
        for sentence_data in result.get('intro_sentences', []):
            paper_numbers = sentence_data.get('paper_numbers', [])
            pmids = []

            for paper_number in paper_numbers:
                if paper_number in paper_index_to_pmid:
                    pmid = paper_index_to_pmid[paper_number]
                    if is_valid_pmid(pmid):
                        pmids.append(pmid)
                        print(f"DEBUG: Converted paper {paper_number} -> PMID {pmid}")
                    else:
                        print(f"DEBUG: Invalid PMID format for paper {paper_number}: {pmid}")
                else:
                    print(f"DEBUG: Invalid paper number in intro_sentences: {paper_number}")

            if pmids:  # 有効なPMIDがある場合のみ追加
                validated_intro_sentences.append({
                    'sentence': sentence_data.get('sentence', ''),
                    'pmids': pmids
                })

        result['recommended_citations'] = validated_citations
        result['intro_sentences'] = validated_intro_sentences

        print(f"DEBUG: Validated {len(validated_citations)} citations and {len(validated_intro_sentences)} intro sentences")
        return result

    except Exception as e:
        print(f"DEBUG: Error in analyze_author_research_history: {e}")
        return {"analysis": "", "recommended_citations": [], "intro_sentences": []}

# ───── 5. GPT で上位数本に絞る ─────────────────────
def select_refs_with_llm(refs, sent, max_refs, progress=None):
    """
    refs: score>=4 の候補
    戻り値: (selected_max_refs, explanation_text)
    """
    if len(refs) <= max_refs:
        return refs, "fewer than max_refs"

    # ── LLM に渡すテーブルを作成 ──────────────────
    pool = sorted(refs, key=lambda x: -x["score"])[:15]      # 上位 15 本だけ渡す
    table = "\n".join(f"{i+1}. PMID:{r['pmid']} Score:{r['score']} {r['title'][:60]}"
                      for i, r in enumerate(pool))

    prompt = (
        "You are an expert editor. Choose the best "
        f"{max_refs} papers for the statement and explain briefly.\n"
        "Respond ONLY with a numbered or PMID list.\n"
        f"Statement: {sent}\n\n{table}"
    )
    
    push(progress, {"step": "gpt", "msg": f"[selector] {len(pool)} papers"})
    #if progress:
    #    progress({"step": "gpt", "msg": f"[selector] {len(pool)} papers"})

    rsp = openai_api_client.chat.completions.create(
        model="o4-mini",
        messages=[{"role": "user", "content": prompt}]
    )
    add_usage("o4-mini", rsp)
    explanation = rsp.choices[0].message.content.strip()
    
    push(progress,{"step":"selector_raw", "msg": explanation[:300]} )
    #if progress:
    #    progress({"step":"selector_raw", "msg": explanation[:300]})  # 先頭300文字

    # ── ① 行頭の番号 1. 2) … を拾う ─────────────────
    nums = [int(n)-1 for n in re.findall(r'^\s*(\d+)[\.\)]?', explanation, re.M)]
    selected = [pool[i] for i in nums if 0 <= i < len(pool)]

    # ── ② PMID: xxxxxxxx を拾う ─────────────────────
    pmids = re.findall(r'PMID[:\s]*(\d{6,8})', explanation, re.I)
    pool_map = {r["pmid"]: r for r in pool}
    for pm in pmids:
        if pm in pool_map and pool_map[pm] not in selected:
            selected.append(pool_map[pm])

    # ── ③ 裸の 8 桁数値を拾う ──────────────────────
    if len(selected) < max_refs:
        nums_raw = re.findall(r'\b(\d{6,8})\b', explanation)
        for pm in nums_raw:
            if pm in pool_map and pool_map[pm] not in selected:
                selected.append(pool_map[pm])
            if len(selected) == max_refs:
                break

    # ── ④ まだ不足していたらスコア順で補完 ────────────
    if len(selected) < max_refs:
        explanation += "\n(fallback: highest-score papers inserted)"
        for r in pool:
            if r not in selected:
                selected.append(r)
            if len(selected) == max_refs:
                break

    final_selected = selected[:max_refs]
    print(f"DEBUG: select_refs_with_llm selected {len(final_selected)} from {len(refs)} candidates")
    if final_selected:
        print(f"DEBUG: LLM selected PMIDs: {[p.get('pmid') for p in final_selected]}")
    return final_selected, explanation

# ───── 6. SubordinateAgent ─────────────────────────
class SubordinateAgent:
    def __init__(self, hits, threshold, max_refs, row_cb=None, enable_advanced_features=False):
        self.hits = hits
        self.th = threshold
        self.max = max_refs
        self.row_cb = row_cb
        self.enable_advanced_features = enable_advanced_features

    def analyze_sentence(self, sent, idx, progress=None):
        push(progress,{"step":"keyword","msg":f"文 {idx+1}: キーワード抽出中..."} )

        kw = extract_keywords(sent)
        push(progress,{"step":"keyword","msg":f"文 {idx+1}: キーワード抽出完了 ({len(kw)}個)"} )

        queries = build_queries(kw)
        push(progress,{"step":"query","msg":f"文 {idx+1}: 検索クエリ生成完了 ({len(queries)}個)"} )

        detail_rows = []
        pool, seen = [], set()
        score5_cnt = 0
        low_cnt    = 0

        for qi, q in enumerate(queries, 1):
            push(progress, {"step":"search","msg":f"文 {idx+1}: PubMed検索 {qi}/{len(queries)} 実行中..."})
            # enable_advanced_featuresに基づいて機能を切り替え
            cands = enhanced_citation_pipeline(sent, q, self.hits, progress, self.enable_advanced_features)
            push(progress, {"step":"search","msg":f"文 {idx+1}: 検索 {qi}/{len(queries)} 完了 ({len(cands)}件取得)"})
            for p in cands:
                if not p or p["pmid"] in seen:
                    continue
                seen.add(p["pmid"])

                detail_rows.append({
                    "sent_idx":idx,"phase":"rank","sentence":sent,
                    "keyword":", ".join(kw),"query":q,
                    "pmid":p.get("pmid", ""),"title":p.get("title", ""),
                    "score":p.get("score", 0),"reason":p.get("citable_decision", ""),
                    "accepted":False,"explanation":""
                })

                score = p.get("score", 0)
                print(f"DEBUG: Paper PMID {p.get('pmid', 'N/A')}: score={score}, title={p.get('title', 'N/A')[:50]}...")
                print(f"DEBUG: Paper reason: {p.get('citable_decision', 'N/A')}")
                # 正しい属性名を使用
                threshold = self.th
                print(f"DEBUG: Using threshold: {threshold}")
                if score >= threshold:
                    pool.append(p)
                    print(f"DEBUG: Added to pool (score >= {threshold})")
                else:
                    low_cnt += 1
                    print(f"DEBUG: Rejected (score < {threshold})")
                if score == 5:
                    score5_cnt += 1

            if score5_cnt >= 3:     
                break

        push(progress,{"step":"select","msg":f"文 {idx+1}: 候補評価完了 ({len(pool)}件が閾値以上)"} )

        notice_msg =""
        if pool:
            push(progress, {"step":"select","msg":f"文 {idx+1}: AI評価で {len(pool)}件から{self.max}件を選択中..."})
            selected, why = select_refs_with_llm(pool, sent, self.max, progress)
            if not selected:
                notice_msg = (f"No paper was finally selected (although {len(pool)} "
                      f"papers scored ≥4). Try increasing 'Hits'.")
                why = notice_msg
                push(progress, {"step": "notice", "msg": notice_msg})
            else:
                push(progress, {"step":"select","msg":f"文 {idx+1}: AI選択完了 ({len(selected)}件選択)"})
        else:
            notice_msg = (f"No reference scored ≥4 (found {low_cnt} papers scored 1–3). "
                  "Try increasing 'Hits' (e.g. 50 or 100) and run again.")
            push(progress,{"step":"notice","msg":notice_msg} )
            selected, why = [], notice_msg

        # accepted フラグを付与
        accepted_set = {p["pmid"] for p in selected}
        for row in detail_rows:
            row["accepted"] = row["pmid"] in accepted_set

        detail_rows.append({
            "sent_idx":idx,"phase":"select","sentence":sent,
            "keyword":", ".join(kw),"query":"",
            "pmid":"","title":"","score":"","reason":"",
            "accepted":"","explanation":why
        })
        # Notice 行がある場合は detail_rows にも残す
        if notice_msg:
            detail_rows.append({
                "sent_idx": idx, "phase": "notice", "sentence": sent,
                "keyword": ", ".join(kw), "query": "",
                "pmid": "", "title": "", "score": "", "reason": "",
                "accepted": "", "explanation": notice_msg
            })

        if self.row_cb:
            for r in detail_rows:
                self.row_cb(r)
        logging.debug("[select] sent_idx=%s  selected=%s", idx,
                    [p.get("pmid") for p in selected])

        final_selected = sorted(selected, key=lambda x: -x.get("score", 0))
        print(f"DEBUG: analyze_sentence returning {len(final_selected)} references for sentence {idx}")
        if final_selected:
            print(f"DEBUG: Final selected PMIDs: {[p.get('pmid') for p in final_selected]}")
        else:
            print(f"DEBUG: No references selected for sentence {idx}")
            print(f"DEBUG: Pool had {len(pool)} candidates, threshold was {self.th}")

        return final_selected

# ───── 7. EditAgent ────────────────────────────────
class EditAgent:
    def insert_and_export_endnote(self, sents, all_refs,
                                  max_refs, prefix: str | None = None):
        if prefix:
            txt, risf = f"{prefix}.txt", f"{prefix}.ris"

        lines, ris_blocks, seen, num = [], [], set(), 1
        for i, (s, refs) in enumerate(zip(sents, all_refs)):
            print(f"DEBUG: EditAgent processing sentence {i}: {len(refs or [])} refs")
            if refs:
                print(f"DEBUG: EditAgent refs PMIDs: {[r.get('pmid') for r in refs]}")
            logging.debug("EditAgent refs=%s", [r.get("pmid") for r in refs or []])  # ←追加
            if refs is None:
                refs = []
            refs = [p for p in refs if p]
            refs = refs[:max_refs]
            tag = "; ".join(endnote_tag(p).strip("{}") for p in refs)
            print(f"DEBUG: Generated tag for sentence {i}: '{tag}'")
            if tag:
                # 日本語の場合：「。」の直前に挿入
                if "。" in s:
                    pos = s.rfind("。")
                    s = f"{s[:pos]} {{{tag}}}。{s[pos+1:]}"
                    print(f"DEBUG: Inserted tag before Japanese period at position {pos}")
                # 英語の場合：「.」の直前に挿入
                elif "." in s:
                    pos = s.rfind(".")
                    s = f"{s[:pos]} {{{tag}}}.{s[pos+1:]}"
                    print(f"DEBUG: Inserted tag before English period at position {pos}")
                # 句点がない場合：文末に追加
                else:
                    s = f"{s} {{{tag}}}"
                    print(f"DEBUG: Appended tag to end of sentence")
            lines.append(s)

            for p in refs:
                if p["pmid"] in seen:
                    continue
                seen.add(p["pmid"])
                ris_blocks.append(to_ris(p, num))
                num += 1

        # 段落構造を保持して文を結合
        # 元のテキストの改行パターンを分析
        original_text = "\n".join(sents)

        # 元のテキストで連続する改行（段落区切り）を特定
        paragraph_breaks = []
        current_pos = 0
        for i, sent in enumerate(sents):
            if i < len(sents) - 1:
                # 現在の文の終了位置を計算
                sent_end = current_pos + len(sent)
                # 次の文の開始位置を探す
                next_sent_start = original_text.find(sents[i + 1], sent_end)
                if next_sent_start > sent_end:
                    # 文間のテキストを取得
                    between_text = original_text[sent_end:next_sent_start]
                    # 連続する改行があるかチェック（段落区切り）
                    if '\n\n' in between_text or between_text.count('\n') > 1:
                        paragraph_breaks.append(i)
                current_pos = next_sent_start if next_sent_start != -1 else sent_end

        # 段落構造を保持して結合
        result_lines = []
        for i, line in enumerate(lines):
            result_lines.append(line)
            if i in paragraph_breaks:
                result_lines.append('')  # 段落区切りとして空行を追加

        cited = "\n".join(result_lines)
        print(f"DEBUG: EditAgent final cited text length: {len(cited)}")
        print(f"DEBUG: EditAgent cited preview: {cited[:500]}...")
        if "{" in cited:
            print("DEBUG: Citation tags found in final cited text")
        else:
            print("DEBUG: NO citation tags found in final cited text")

        if prefix:
            open(txt, "w", encoding="utf-8").write(cited + "\n")
            open(risf, "w", encoding="utf-8").write("\n".join(ris_blocks))
        return cited, "\n".join(ris_blocks)

# ───── 8. レポート書き出し ──────────────────────────
def write_report(rows: List[Dict]) -> str:
    tmp  = tempfile.gettempdir()
    base = str(uuid.uuid4())
    csvf = os.path.join(tmp, base + ".csv")
    jsf  = os.path.join(tmp, base + ".json")
    with open(csvf, "w", newline="", encoding="utf-8") as f:
        if rows:
            w = csv.DictWriter(f, fieldnames=rows[0].keys())
            w.writeheader(); w.writerows(rows)
        else:
            f.write("sent_idx,pmid,score,reason,accepted\n")
    open(jsf, "w", encoding="utf-8").write(
        json.dumps(rows, ensure_ascii=False, indent=2)
    )
    return base

def write_detail_report(rows: List[Dict]) -> str:
    tmp  = tempfile.gettempdir()
    base = str(uuid.uuid4())
    csvf = os.path.join(tmp, base + "_detail.csv")
    jsf  = os.path.join(tmp, base + "_detail.json")
    # extrasaction='ignore' で列が増えても落ちないようにする
    with open(csvf, "w", newline="", encoding="utf-8") as f:
        w = csv.DictWriter(f, fieldnames=DETAIL_HEADERS, extrasaction="ignore")
        w.writeheader()
        w.writerows(rows)
    open(jsf, "w", encoding="utf-8").write(
        json.dumps(rows, ensure_ascii=False, indent=2)
    )
    return base

def is_intro_section(text: str, sents: List[str], sent_idx: int) -> bool:
    """
    指定された文がイントロダクションセクションに属するかを判定
    """
    # 簡単な実装：文の位置から判定
    # より正確な実装は、セクション境界を詳細に分析する必要がある
    intro_keywords = ["イントロダクション", "はじめに", "序論", "Introduction"]
    discussion_keywords = ["考察", "ディスカッション", "Discussion"]

    # 文の前後のテキストを確認
    sentence = sents[sent_idx]
    text_before = text[:text.find(sentence)]

    # 最後に出現したセクション見出しを確認
    last_intro_pos = -1
    last_discussion_pos = -1

    for keyword in intro_keywords:
        pos = text_before.rfind(keyword)
        if pos > last_intro_pos:
            last_intro_pos = pos

    for keyword in discussion_keywords:
        pos = text_before.rfind(keyword)
        if pos > last_discussion_pos:
            last_discussion_pos = pos

    # イントロダクションの方が後に出現していればイントロセクション
    return last_intro_pos > last_discussion_pos

def find_optimal_insertion_point(sents: List[str], target_sentence_indices: List[int]) -> int:
    """
    著者の文章を挿入する最適な位置を特定

    Args:
        sents: 文リスト
        target_sentence_indices: 対象文のインデックス

    Returns:
        int: 挿入位置のインデックス
    """
    intro_indices = [i for i in target_sentence_indices if is_intro_section(' '.join(sents), sents, i)]

    if not intro_indices:
        return 0

    # イントロダクションの文を分析して最適な挿入位置を決定
    for i, sent_idx in enumerate(intro_indices):
        sentence = sents[sent_idx].lower()

        # 「本研究では」「我々は」などの表現がある文の前に挿入
        if any(phrase in sentence for phrase in [
            "本研究では", "本研究において", "我々は", "今回", "そこで",
            "this study", "we", "here", "therefore"
        ]):
            print(f"DEBUG: Found optimal insertion point before sentence {sent_idx}: {sentence[:50]}...")
            return sent_idx

    # 最適な位置が見つからない場合は、イントロダクションの中間に挿入
    mid_point = len(intro_indices) // 2
    insert_position = intro_indices[mid_point] if mid_point < len(intro_indices) else intro_indices[-1]
    print(f"DEBUG: Using mid-point insertion at sentence {insert_position}")
    return insert_position

def insert_author_sentences(sents: List[str], refs_all: List[List[Dict]],
                           author_sentences_with_refs: List[Dict],
                           target_sentence_indices: List[int]) -> tuple:
    """
    著者の過去研究を紹介する新しい文章をイントロダクションに挿入

    Args:
        sents: 既存の文リスト
        refs_all: 既存の引用リスト
        author_sentences_with_refs: 著者の新しい文章と引用
        target_sentence_indices: 対象文のインデックス

    Returns:
        tuple: (更新された文リスト, 更新された引用リスト)
    """
    # 最適な挿入位置を特定
    insert_position = find_optimal_insertion_point(sents, target_sentence_indices)

    # 新しい文章を挿入
    new_sents = sents.copy()
    new_refs_all = refs_all.copy()

    for i, sentence_data in enumerate(author_sentences_with_refs):
        sentence_text = sentence_data['sentence']
        sentence_refs = sentence_data['refs']

        # 文章を挿入
        insert_idx = insert_position + i
        new_sents.insert(insert_idx, sentence_text)

        # 引用リストを挿入
        new_refs_all.insert(insert_idx, sentence_refs)

        print(f"DEBUG: Inserted author sentence at position {insert_idx}: {sentence_text[:50]}...")
        print(f"DEBUG: Rationale: {sentence_data.get('rationale', 'No rationale provided')}")

    return new_sents, new_refs_all

# ───── 9. パイプライン本体 ───────────────────────────
def run_pipeline_with_author_priority(text: str,
                                     hits: int = 10,
                                     threshold: int = 4,
                                     max_refs: int = 3,
                                     generate_files: bool = False,
                                     out_prefix: str | None = None,
                                     progress=None,
                                     author_last_name: str = None,
                                     author_first_name: str = None):
    """
    著者優先の新しいパイプライン関数
    """
    push(progress, {"step":"start","msg":"pipeline start with author priority"})

    # 著者の過去の研究を検索・分析
    author_analysis = None
    if author_last_name and author_first_name:
        if progress:
            progress({"step": "author_search", "message": f"著者 {author_last_name} {author_first_name} の過去の研究を検索中..."})

        author_papers = search_author_publications(author_last_name, author_first_name)

        if author_papers:
            if progress:
                progress({"step": "author_analysis", "message": f"著者の {len(author_papers)} 件の論文を分析中..."})

            author_analysis = analyze_author_research_history(author_papers, text)
            print(f"DEBUG: Author analysis completed. Found {len(author_analysis.get('recommended_citations', []))} recommended citations")

    # 従来のパイプライン処理を実行（著者分析結果を統合）
    return run_pipeline_traditional(text, hits, threshold, max_refs, generate_files, out_prefix, progress, author_analysis)

def run_pipeline_traditional(text: str, hits: int, threshold: int, max_refs: int,
                           generate_files: bool, out_prefix: str, progress, author_analysis=None, enable_advanced_features=False):
    """
    従来のパイプライン処理（著者分析結果を統合可能）
    """
    sents = split_sentences(text)
    target_sentence_indices = identify_target_sentences(text, sents, enable_advanced_features)

    refs_all: List[List[Dict]] = [None]*len(sents)
    report_rows:  List[Dict]   = []

    # 著者分析結果を統合（新しい文章として追加）
    author_sentences_with_refs = []
    if author_analysis and author_analysis.get('intro_sentences'):
        for sentence_data in author_analysis['intro_sentences']:
            sentence_text = sentence_data.get('sentence', '')
            pmids = sentence_data.get('pmids', [])

            if sentence_text and pmids:
                # 著者の推奨論文を準備
                author_refs = []
                for pmid in pmids:
                    for citation in author_analysis['recommended_citations']:
                        if citation.get('pmid') == pmid:
                            author_refs.append({
                                'pmid': pmid,
                                'title': citation.get('title', ''),
                                'authors': citation.get('authors', []),
                                'year': citation.get('year', ''),
                                'journal': citation.get('journal', ''),
                                'score': 5,  # 著者の論文は最高スコア
                                'citable_decision': f"著者の過去の研究: {citation.get('relevance_reason', '')}"
                            })

                if author_refs:
                    author_sentences_with_refs.append({
                        'sentence': sentence_text,
                        'refs': author_refs,
                        'rationale': sentence_data.get('rationale', 'GPT生成文章')
                    })
                    print(f"DEBUG: Prepared author sentence with {len(author_refs)} references")
                    print(f"DEBUG: Sentence rationale: {sentence_data.get('rationale', 'No rationale')}")

    # 著者の新しい文章を文リストに挿入
    if author_sentences_with_refs:
        sents, refs_all = insert_author_sentences(sents, refs_all, author_sentences_with_refs, target_sentence_indices)
        # target_sentence_indices を更新（新しい文が追加されたため）
        target_sentence_indices = identify_target_sentences(' '.join(sents), sents, enable_advanced_features)

    # 従来の処理を継続（以下は既存のrun_pipeline関数の内容）
    q_detail: Queue = Queue()
    def collect_row(row:dict): q_detail.put(row)

    def task(i, sent):
        if i not in target_sentence_indices:
            print(f"DEBUG: Skipping sentence {i} (not in target sections)")
            refs_all[i] = []
            return

        if progress:
            progress({"step": "sentence", "message": f"文 {i+1}/{len(sents)} を処理中: キーワード抽出..."})

        ag = SubordinateAgent(hits, threshold, max_refs, row_cb=collect_row, enable_advanced_features=enable_advanced_features)
        try:
            refs = ag.analyze_sentence(sent, i, progress)
            print(f"DEBUG: Sentence {i} returned {len(refs or [])} selected references")
            if refs:
                print(f"DEBUG: Selected PMIDs for sentence {i}: {[r.get('pmid') for r in refs]}")
                if progress:
                    progress({"step": "sentence", "message": f"文 {i+1}/{len(sents)} 完了: {len(refs)}件の文献を選択"})
            else:
                if progress:
                    progress({"step": "sentence", "message": f"文 {i+1}/{len(sents)} 完了: 関連文献なし"})
        except Exception as e:
            import logging
            logging.exception("analyze_sentence failed (sent_idx=%s): %s", i, e)
            refs = []
            if progress:
                progress({"step": "sentence", "message": f"文 {i+1}/{len(sents)} エラー: {str(e)[:50]}..."})

        # 既存の refs_all[i] がある場合は統合
        if refs_all[i] is None:
            refs_all[i] = refs or []
        else:
            refs_all[i].extend(refs or [])

        for r in refs or []:
            report_rows.append({
                "sent_idx": i,
                "pmid": r["pmid"],
                "score": r["score"],
                "reason": r["citable_decision"],
                "accepted": True
            })

    # 並列で各文を処理（並列度を2に制限してサーバー負荷軽減）
    with ThreadPoolExecutor(max_workers=2) as pool:
        [pool.submit(task, i, s) for i, s in enumerate(sents)]
        pool.shutdown(wait=True)

    # Queue から detail_rows をまとめる
    detail_rows=[]
    while True:
        try:
            detail_rows.append(q_detail.get_nowait())
        except Empty:
            break

    if progress:
        progress({"step": "integrate", "message": "引用タグを論文テキストに挿入中..."})

    cited, ris = EditAgent().insert_and_export_endnote(
        sents, refs_all, max_refs, out_prefix if generate_files else None
    )

    if progress:
        progress({"step": "integrate", "message": "引用タグ挿入完了"})

    base_rep    = write_report(report_rows)
    base_detail = write_detail_report(detail_rows)

    push(progress, {"step":"cost","usd":round(METER.usd(),4)})

    # 参照リストの構築（重複除去を強化）
    unique = {}
    pmid_to_index = {}  # PMIDから引用番号へのマッピング

    for lst in refs_all:
        if not lst: continue
        for p in enumerate(lst):
            if isinstance(p[1], dict) and 'pmid' in p[1]:
                pmid = p[1]['pmid']
                if pmid not in unique:
                    unique[pmid] = p[1]

    references = list(unique.values())
    print(f"DEBUG: Total unique references found: {len(references)}")

    # メモリ解放: 大きなデータ構造をクリア
    detail_rows.clear()
    del detail_rows
    unique.clear()
    del unique

    # 著者分析結果もクリア
    if author_analysis:
        author_analysis.clear()
        del author_analysis

    # ガベージコレクション実行
    import gc
    gc.collect()
    print("DEBUG: Memory cleanup completed")

    return cited, ris, base_rep, base_detail, references

def run_pipeline_internal(text: str,
                 hits: int = 10,
                 threshold: int = 4,
                 max_refs: int = 3,
                 generate_files: bool = False,
                 out_prefix: str | None = None,
                 progress=None,
                 enable_advanced_features: bool = False):

    push(progress, {"step":"start","msg":"pipeline start"})
    references =[]
    #if progress:
    #    progress({"step":"start","msg":"pipeline start"})

    sents = split_sentences(text)

    # 対象文を特定（Citer2では全文、Writerではイントロ・考察のみ）
    target_sentence_indices = identify_target_sentences(text, sents, enable_advanced_features)
    if enable_advanced_features:
        print(f"DEBUG: Identified {len(target_sentence_indices)} target sentences in intro/discussion sections")
    else:
        print(f"DEBUG: Processing all {len(target_sentence_indices)} sentences (Citer2 mode)")

    refs_all: List[List[Dict]] = [None]*len(sents)
    report_rows:  List[Dict]   = []

    # ---------- ① detail 行を thread-safe に収集する ------------
    q_detail: Queue = Queue()

    def collect_row(row:dict): q_detail.put(row)
    # ------------------------------------------------------------

    def task(i, sent):
        # 対象セクション（イントロ・考察）の文のみを処理
        if i not in target_sentence_indices:
            print(f"DEBUG: Skipping sentence {i} (not in target sections)")
            refs_all[i] = []
            return

        if progress:
            progress({"step": "sentence", "message": f"文 {i+1}/{len(sents)} を処理中: キーワード抽出..."})

        ag = SubordinateAgent(hits, threshold, max_refs, row_cb=collect_row, enable_advanced_features=enable_advanced_features)
        try:
            refs = ag.analyze_sentence(sent, i, progress)
            print(f"DEBUG: Sentence {i} returned {len(refs or [])} selected references")
            if refs:
                print(f"DEBUG: Selected PMIDs for sentence {i}: {[r.get('pmid') for r in refs]}")
                if progress:
                    progress({"step": "sentence", "message": f"文 {i+1}/{len(sents)} 完了: {len(refs)}件の文献を選択"})
            else:
                if progress:
                    progress({"step": "sentence", "message": f"文 {i+1}/{len(sents)} 完了: 関連文献なし"})
        except Exception as e:
            import logging
            logging.exception("analyze_sentence failed (sent_idx=%s): %s", i, e)
            refs = []
            if progress:
                progress({"step": "sentence", "message": f"文 {i+1}/{len(sents)} エラー: {str(e)[:50]}..."})
        refs_all[i] = refs

        for r in refs or []:
            # 将来のリライト機能のため、文の種類情報も保存
            sentence_type = "unknown"
            if enable_advanced_features:
                try:
                    from agent.writer_features import classify_sentence_type
                    classification = classify_sentence_type(sent)
                    sentence_type = classification.get("sentence_type", "unknown")
                except:
                    sentence_type = "unknown"

            report_rows.append({
                "sent_idx": i,
                "sentence": sent,
                "sentence_type": sentence_type,
                "pmid": r["pmid"],
                "score": r["score"],
                "reason": r["citable_decision"],
                "accepted": True,
                "potential_rewrite_use": sentence_type in ["current_findings", "author_opinion"]  # リライト候補
            })

    # 並列で各文を処理（並列度を2に制限してサーバー負荷軽減）
    with ThreadPoolExecutor(max_workers=2) as pool:
        [pool.submit(task, i, s) for i, s in enumerate(sents)]
        pool.shutdown(wait=True)

    # ---------- ② Queue から detail_rows をまとめる ------------
    detail_rows=[]
    while True:
        try:
            detail_rows.append(q_detail.get_nowait())
        except Empty:
            break
    # ------------------------------------------------------------

    if progress:
        progress({"step": "integrate", "message": "引用タグを論文テキストに挿入中..."})

    cited, ris = EditAgent().insert_and_export_endnote(
        sents, refs_all, max_refs, out_prefix if generate_files else None
    )

    if progress:
        progress({"step": "integrate", "message": "引用タグ挿入完了"})

    base_rep    = write_report(report_rows)
    base_detail = write_detail_report(detail_rows)

    push(progress, {"step":"cost","usd":round(METER.usd(),4)})
    #if progress:
    #    progress({"step":"cost","usd":round(METER.usd(),4)})

    # デバッグ情報を追加
    print(f"DEBUG: refs_all length: {len(refs_all)}")
    non_empty_refs = [refs for refs in refs_all if refs]
    print(f"DEBUG: Non-empty refs count: {len(non_empty_refs)}")
    if non_empty_refs:
        print(f"DEBUG: First non-empty refs: {non_empty_refs[0]}")

    unique = {}
    for i, lst in enumerate(refs_all):
        if not lst: continue
        print(f"DEBUG: Processing refs_all[{i}] with {len(lst)} items")
        for j, p in enumerate(lst):
            print(f"DEBUG: Item {j}: type={type(p)}, keys={list(p.keys()) if isinstance(p, dict) else 'N/A'}")
            if isinstance(p, dict) and 'pmid' in p:
                pmid = p['pmid']
                unique[pmid] = p
                print(f"DEBUG: Added PMID {pmid} to unique references")
            else:
                print(f"DEBUG: Skipping item - no pmid key or not dict: {p}")

    references = list(unique.values())
    print(f"DEBUG: Total unique references found: {len(references)}")
    if references:
        print(f"DEBUG: First reference: {references[0]}")

    # テキスト整理を適用
    try:
        if enable_advanced_features:
            # Writer用: 高度な整理機能
            from agent.writer_features import comprehensive_writer_cleanup
            cited = comprehensive_writer_cleanup(cited)
            print("DEBUG: Applied comprehensive writer cleanup")
        else:
            # Citer2用: 基本的な整理機能
            from agent.citer2_features import simple_citation_cleanup
            cited = simple_citation_cleanup(cited)
            print("DEBUG: Applied basic citation cleanup for Citer2")
    except Exception as e:
        print(f"DEBUG: Text cleanup failed, using original: {e}")

    return cited, ris, base_rep, base_detail, references

def run_pipeline_for_writer(text: str,
                 hits: int = 10,
                 threshold: int = 4,
                 max_refs: int = 3,
                 generate_files: bool = False,
                 out_prefix: str | None = None,
                 progress=None):
    """
    Writer用の高度な機能を有効にしたパイプライン
    """
    return run_pipeline_internal(text, hits, threshold, max_refs, generate_files, out_prefix, progress, enable_advanced_features=True)

def run_pipeline(text: str,
                 hits: int = 10,
                 threshold: int = 3,  # Citer2では閾値を3に下げて有用な文献を含める
                 max_refs: int = 3,
                 generate_files: bool = False,
                 out_prefix: str | None = None,
                 progress=None):
    """
    Citer2用の基本機能のみのパイプライン
    """
    return run_pipeline_internal(text, hits, threshold, max_refs, generate_files, out_prefix, progress, enable_advanced_features=False)


# ───── 10. CLI テスト ─────────────────────────────
if __name__ == "__main__":
    import sys, pathlib
    fname = input("txt file (empty=stdin): ").strip()
    full  = pathlib.Path(fname).read_text(encoding="utf-8") if fname else sys.stdin.read()
    cited, ris, _, _, references = run_pipeline(full)
    print(cited)
    print("\n--- RIS ---\n", ris)
