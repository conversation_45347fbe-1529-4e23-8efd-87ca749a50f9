2025-06-01 更新

<<<<重要>>>>>>
データベースファイルは、コンテナ内部に作るのではなく、コンテナ外のサーバー領域に永続マウント。
ルートディレクトリに .alembic/versionsフォルダを作成し、コンテナ内の/app/alembic/versionsを同期させる設計とした。


──────────────────────────────
FastAPI-Users ＋ Alembic
「モデルを修正したら DB も更新する」
Docker／docker-compose 運用手順まとめ
──────────────────────────────

前提
• Dockerfile の ENTRYPOINT には
python -m alembic upgrade head && uvicorn …
が書いてあり、起動時に既存リビジョンを適用するだけ。
• compose.yml で
./alembic/versions:/app/alembic/versions
を bind-mount している（＝ホスト側で作ったリビジョンがそのまま使われる）。
• DATABASE_URL などは .env でコンテナに渡している。

──────────────────────────────
① モデルを変更する
──────────────────────────────
例：agent/models.py に

gmail_tokens    = Column(JSON,  nullable=True)
calendar_tokens = Column(JSON,  nullable=True)
stripe_customer_id = Column(String, nullable=True)
などを追加／変更。

──────────────────────────────
② 一時コンテナに入りリビジョンを作る
──────────────────────────────

# Postgres だけ先に起動（DB が必要）
docker compose up -d postgres

# citer2 のシェルに入る（ENTRYPOINT を無効化）
docker compose run --rm --entrypoint /bin/sh citer2
# ── コンテナ内 ─────────────────────

alembic revision --autogenerate -m "general bugfix"   # ← 重要
exit
・生成された .py が /app/alembic/versions に置かれる
→ bind-mount でホスト側 ./alembic/versions にも残る。
・内容を確認し、必要なら手で修正して git add / commit。


＊＊＊＊＊＊＊＊＊＊＊＊＊ダウングレードしなければならないとき＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊		
まず
cd ./alembic/versions
ls -al
でサーバー側に（コンテナの外側）に保存されたリビジョンファイル一覧をみて、
最新のもの（例えば、 ed81f0431e08_add_hiddentodos_schema.py)と、
一つ前のもの（例えば、　22c083375f82_add_oauth_token_columns.py）　を確認する。  

その後、
docker compose down
docker compose up -d postgres
docker compose run --rm --entrypoint /bin/sh citer2
のでciter2のシェルに入った後に、
alembic current
で最新のリヴィジョン名を確認。これを一つ前（ed81f0431e08）に戻したい場合は、
alembic downgrade 22c083375f82
exit

次に、
 docker compose down
 docker compose up -d　（upgradeは自動化してある）
で、コンテナを再起動したあと、
docker ps　　　でpostgresのコンテナIDをmemo
docker exec -it <postgresのcontainer ID> psql -U myaiagentsUSER -d myaiagentsDB
でpsqlを起動し、
\dt 		でテーブル一覧、
\d user   	でuserテーブルの定義を確認などし、
\q 　　		終了

これで、最新の（消したい）リビジョンファイルを削除してもよさそうなら、
念の為バックアップをとって
tar czf alembic_versions_backup/alembic_versions_$(date +%F).tar.gz alembic/versions
のあとに、
rm alembic/versions/ed81f0431e08_add_hiddentodos_schema.py
で消す。 

＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊s＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊：

──────────────────────────────
③ スタックを再起動して適用
──────────────────────────────

docker compose down
docker compose up --build -d
起動ログに
Running upgrade  -> 20250602_xxxx (add oauth token columns)
が出れば ALTER TABLE が実行済み。

──────────────────────────────
④ 動作確認
──────────────────────────────

ブラウザ／API で再ログイン。
500 “column … does not exist” が消えている。
psql で \d "user" を見ても新カラムが存在。
──────────────────────────────
Trouble-shooting チェックリスト
──────────────────────────────
□ ホスト側 ./alembic/versions にリビジョンファイルがあるか
□ alembic.ini がイメージ内 /app に存在するか
□ DATABASE_URL が asyncpg 形式で正しいか
□ Postgres 起動前に upgrade が走っていないか
□ .dockerignore で alembic ディレクトリを除外していないか

──────────────────────────────
運用ベストプラクティス
──────────────────────────────
・モデルを変えたら 開発用コンテナで revision → commit
・本番／CI は upgrade head だけ（自動生成はさせない）
・複数人開発ではリビジョン番号衝突に注意して「一機能一 revision」。

以上を守れば、次回以降も
「モデル追加 → リビジョン作成 → up --build」で
DB とコードを安全に同期できます。



2025-05-31  基本の運用フロー


（１）ローカル側：開発～イメージ push

docker buildx build --platform linux/amd64 -t atmiyashita/citer2:1 --push .

※現状、ローカルではDB構築をしない。
上記で生成されるイメージがlinux/amd64アーキテクチャ用なので、macで動かせない
（mac用のイメージも同時生成して運用することはできるかもしれない）


（２）サーバーからpull/ビルド
ssh rocky@49.212.230.129
cd citer2
docker compose pull && docker compose up -d



（３）サーバーでデータベース構築作業（更新時だけ）
もし、alembic/versions には __init__.pyしか入っていない（他にリビジョンファイルがない）状態なら、

（３）ー１　まずリビジョンを作る（初回だけ）：

docker compose exec citer2 bash -lc "cd /app && python -m alembic revision --autogenerate -m 'initial schema'"

　→ versions フォルダーに
　　 5832cac01bc2_initial_schema.py  などが出来る。 

（３）ー２　その次に、そのリビジョンを適用する

docker compose exec citer2 bash -lc "cd /app && python -m alembic upgrade head"

　→ user / credits / … のテーブルが物理的に作られる。

（３）ー３ 　以降
　• モデルを変更したら 2→1→2 のサイクル
　• DB を消してやり直すときは 1は不要、2（upgrade）だけで OK
docker compose exec citer2 
bash -lc "cd /app && python -m alembic upgrade head"


（４）データベース構造の確認方法

まずpsqlに入る：
docker compose exec postgres psql -U <USER> -d <DB>

※postgresのUSERとDBは.envを参照してください。




=========また、以下の対応は今後必要になります。==============

GPTへの質問：
ちなみに、今ローカルはmacを使っていて、イメージ生成時にwindows/linu用のイメージを生成しています。サーバーサイドはlinuxなので、pull 後の実行環境で全てのテストを行なっています。
ローカルでテスト環境を用意したり、alembicのrevisionファイルもローカルで生成してgit管理した方がいいと思っています。解決策はありますか？


GPTからの回答(o3):

ローカル（Mac/AppleSilicon）で
① 実 DB を含む「動く開発環境」を用意し
② Alembic の revision ファイルをローカルで生成して Git 管理し
③ Linux 本番用イメージを cross-build して push
── という流れに整える具体策をまとめます。

────────────────────────────
0. ディレクトリ構成の前提
────────────────────────────
project/
├─ docker-compose.dev.yml      … 開発専用（volume mount, reload）
├─ docker-compose.yml          … 本番/ステージング用
├─ Dockerfile                  … マルチプラットフォーム対応
├─ alembic/
│   ├─ env.py
│   └─ versions/               … ★ ここを Git で管理
└─ agent/ … ソース

────────────────────────────

開発用 compose ファイル（dev） ──────────────────────────── 例：docker-compose.dev.yml
services:
  db:
    image: postgres:16
    environment:
      POSTGRES_USER: devuser
      POSTGRES_PASSWORD: devpass
      POSTGRES_DB: devdb
    ports: ["5432:5432"]
    volumes: [pgdata_dev:/var/lib/postgresql/data]

  app:
    build: .
    command: uvicorn agent.main:app --host 0.0.0.0 --port 8000 --reload
    environment:
      DATABASE_URL: postgresql+asyncpg://devuser:devpass@db:5432/devdb
    volumes:
      - .:/app        # ← ホストコードをマウント
    depends_on: [db]
    ports: ["8000:8000"]

volumes:
  pgdata_dev:
ポイント
• ホストコードを /app にマウントするので、コンテナ内で生成した
alembic/versions/*.py がローカル側にも即座に反映される。
• --reload 付きでホットリロード。

起動

docker compose -f docker-compose.dev.yml up -d
────────────────────────────
2. Alembic revision をローカルで生成
────────────────────────────

# 生成
docker compose -f docker-compose.dev.yml exec app \
  bash -lc "python -m alembic revision --autogenerate -m 'add foo'"

# 適用して検証
docker compose -f docker-compose.dev.yml exec app \
  bash -lc "python -m alembic upgrade head"
結果:
project/alembic/versions/xxxx_add_foo.py が
Mac 側ファイルシステムに出来る。
Git でコミット → push すれば本番にも伝搬。

────────────────────────────
3. マルチプラットフォーム Build & Push
────────────────────────────
AppleSilicon → Linux amd64 の例

# 一度だけ
docker buildx create --use --name cross && docker buildx inspect --bootstrap

# ビルド＆push
docker buildx build \
  --platform linux/amd64 \
  -t ghcr.io/your-org/citer2:2025-05-31 \
  --push .
• QEMU が自動で使われるので M1/M2 でも amd64 イメージを作成可。
• GitHub Actions で同じコマンドを回せば CI 上でも生成可能。

────────────────────────────
4. 本番サーバー側
────────────────────────────

git pull （compose.yml は prod 用）
docker compose pull # 新イメージ取得
docker compose up -d # 起動
docker compose exec citer2 bash -lc "python -m alembic upgrade head" # ← 手動 or entrypoint 自動
────────────────────────────
5. CI / 自動テスト（お勧め）
────────────────────────────
.github/workflows/ci.yml 例

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: ciuser
          POSTGRES_PASSWORD: cipass
          POSTGRES_DB: cidb
        ports: ['5432:5432']
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with: {python-version: '3.11'}
      - run: pip install -r requirements.txt
      - run: |
          python -m alembic upgrade head   # ← Linux でマイグレーション検証
          pytest
これで「Linux 上でもマイグレーションが通る」ことを自動保証。

────────────────────────────
6. まとめ：日常運用フロー
────────────────────────────
ローカル(Mac)

コード修正 → docker compose -f docker-compose.dev.yml up
alembic revision --autogenerate → upgrade head
単体テスト & ブラウザ動作確認
git commit && git push
docker buildx build --platform linux/amd64 --push
本番(Linux)

ssh → git pull → docker compose pull
docker compose up -d
python -m alembic upgrade head（手動 or 自動）
Google SSO / Stripe Webhook 簡易動作確認
これで
• revision ファイルを必ず Git 管理
• Mac ⇄ Linux 間の動作差異を CI と buildx で吸収
という安全なデプロイサイクルが構築できます。

